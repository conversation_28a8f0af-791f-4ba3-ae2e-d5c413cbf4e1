/**
 * AI塔罗牌解读服务
 * 支持对接OpenAI GPT和DeepSeek API
 */

import { getCurrentAIConfig } from './ai-config';

export interface TarotCard {
  name: string;
  imageUrl?: string;
  orientation: '正位' | '逆位';
  position?: string; // 在牌阵中的位置含义，如"过去"、"现在"、"未来"
}

export interface AIInterpretationRequest {
  question: string;
  cards: TarotCard[];
  spreadType: string;
  readingType: string;
}

export interface AIInterpretationResponse {
  interpretation: string;
  success: boolean;
  error?: string;
}

/**
 * 构建塔罗牌解读的Prompt
 */
function buildTarotPrompt(request: AIInterpretationRequest): string {
  const { question, cards, spreadType, readingType } = request;

  // 增强的牌面描述，包含更多上下文
  const cardsDescription = cards.map((card, index) => {
    const position = card.position || `第${index + 1}张牌`;
    return `${position}: ${card.name} (${card.orientation})`;
  }).join('\n');

  // 分析牌面组合特征
  const cardAnalysis = analyzeCardCombination(cards);

  // 根据问题类型调整解读风格
  const questionType = categorizeQuestion(question);
  const styleGuide = getStyleGuide(questionType, cards.length);

  return `你是一位专业的塔罗牌解读师，擅长用通俗易懂的语言为现代人提供实用的人生指引。

【用户问题】
${question}

【占卜类型】
${readingType}

【使用牌阵】
${spreadType}

【抽到的牌】
${cardsDescription}

【解读要求】
1. **直接回答用户问题** - 基于塔罗牌给出明确的指引和建议
2. **详细解释牌面含义** - 说明每张牌的含义，特别是正逆位的区别
3. **连接问题与牌面** - 解释为什么这些牌能回答用户的问题
4. **提供实用建议** - 给出具体可行的行动指导
5. **语言风格** - 温暖友善，通俗易懂，避免过于玄奥的表达
6. **内容限制** - 只输出以下指定的三个部分，不要添加其他内容

请严格按照以下格式输出，不要添加任何其他部分：

## 🔮 塔罗指引

**对于您的问题"${question}"，塔罗牌的指引是：[直接回答]**

## 📜 牌面解析

[详细解释每张牌的含义和为什么这样解读]

## 💡 行动建议

[具体的行动建议和注意事项]

请开始解读：`;
}

/**
 * 分析牌面组合特征
 */
function analyzeCardCombination(cards: TarotCard[]): string {
  const majorCount = cards.filter(card => card.name.includes('大阿卡纳') ||
    ['愚者', '魔术师', '女祭司', '皇后', '皇帝', '教皇', '恋人', '战车', '力量', '隐者', '命运之轮', '正义', '倒吊人', '死神', '节制', '恶魔', '塔', '星星', '月亮', '太阳', '审判', '世界'].some(major => card.name.includes(major))
  ).length;

  const reversedCount = cards.filter(card => card.orientation === '逆位').length;

  let analysis = '';

  if (majorCount > cards.length / 2) {
    analysis += '大阿卡纳牌较多，表示这是一个重要的人生主题，具有深远影响。';
  }

  if (reversedCount > cards.length / 2) {
    analysis += '逆位牌较多，提示需要内省和调整，可能存在阻碍需要克服。';
  } else if (reversedCount === 0) {
    analysis += '全部正位，能量流动顺畅，是积极发展的好兆头。';
  }

  return analysis || '牌面组合平衡，显示情况正在稳步发展。';
}

/**
 * 问题类型分类
 */
function categorizeQuestion(question: string): string {
  const loveKeywords = ['爱情', '感情', '恋爱', '婚姻', '伴侣', '喜欢', '分手', '复合'];
  const careerKeywords = ['工作', '事业', '职业', '升职', '跳槽', '创业', '面试', '同事'];
  const financeKeywords = ['财运', '金钱', '投资', '理财', '收入', '财富', '经济'];
  const healthKeywords = ['健康', '身体', '疾病', '治疗', '养生'];
  const spiritualKeywords = ['精神', '灵性', '成长', '修行', '觉醒', '内心'];

  if (loveKeywords.some(keyword => question.includes(keyword))) {
    return '情感关系类问题 - 需要关注情感能量和人际互动';
  } else if (careerKeywords.some(keyword => question.includes(keyword))) {
    return '事业发展类问题 - 重点关注行动力和外在表现';
  } else if (financeKeywords.some(keyword => question.includes(keyword))) {
    return '财富运势类问题 - 注意物质层面的能量流动';
  } else if (healthKeywords.some(keyword => question.includes(keyword))) {
    return '健康相关问题 - 关注身心平衡和生命力';
  } else if (spiritualKeywords.some(keyword => question.includes(keyword))) {
    return '精神成长类问题 - 深入内在智慧和灵性指引';
  }

  return '综合生活问题 - 需要全面平衡的视角';
}

/**
 * 获取解读风格指导
 */
function getStyleGuide(questionType: string, cardCount: number): string {
  let guide = '';

  if (cardCount === 1) {
    guide = '单牌解读 - 专注核心信息，给出清晰直接的指引';
  } else if (cardCount <= 3) {
    guide = '简单牌阵 - 注重时间线或不同角度的分析';
  } else {
    guide = '复杂牌阵 - 需要深度分析各个层面的相互关系';
  }

  if (questionType.includes('情感')) {
    guide += '，语言温暖感性，关注情感细节';
  } else if (questionType.includes('事业')) {
    guide += '，语言务实理性，提供具体行动方案';
  } else if (questionType.includes('精神')) {
    guide += '，语言富有哲理，启发内在思考';
  }

  return guide;
}

/**
 * 调用OpenAI API
 */
async function callOpenAI(prompt: string, apiKey: string): Promise<string> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.7
    })
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data.choices[0]?.message?.content || '解读生成失败';
}

/**
 * 调用DeepSeek API
 */
async function callDeepSeek(prompt: string, apiKey: string, baseURL: string): Promise<string> {
  const response = await fetch(`${baseURL}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.7
    })
  });

  if (!response.ok) {
    throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data.choices[0]?.message?.content || '解读生成失败';
}

/**
 * 本地模拟解读（作为备用方案）
 */
function generateLocalInterpretation(request: AIInterpretationRequest): string {
  const { question, cards } = request;

  return `## 🔮 塔罗指引

**对于您的问题"${question}"，塔罗牌的指引是：**

根据您抽到的牌面，我看到了积极的发展趋势。塔罗牌建议您保持开放的心态，相信自己的直觉和判断力。

## 📜 牌面解析

${cards.map((card, index) => {
  const meanings = {
    '正位': '这张牌呈现正位，表示能量流动顺畅，是积极发展的好兆头',
    '逆位': '这张牌呈现逆位，提示您需要内省和调整，可能存在需要克服的阻碍'
  };

  return `**${card.position || `第${index + 1}张牌`}**: ${card.name} (${card.orientation})

${meanings[card.orientation]}。${card.name}在您的问题中代表着重要的人生主题，需要您特别关注和思考。`;
}).join('\n\n')}

## 💡 行动建议

根据牌面显示，建议您：

1. **保持积极心态** - 相信自己的直觉和判断，每个挑战都是成长的机会
2. **采取实际行动** - 将内心的想法转化为具体的行动计划
3. **保持耐心** - 好的结果需要时间，不要急于求成
4. **倾听内心** - 在做决定时，多听听自己内心真实的声音

*请记住：塔罗牌提供的是指引和启发，最终的决定权始终在您手中。*`;
}

/**
 * 主要的AI解读函数
 */
export async function getAIInterpretation(request: AIInterpretationRequest): Promise<AIInterpretationResponse> {
  try {
    const config = getCurrentAIConfig();
    const prompt = buildTarotPrompt(request);
    
    let interpretation: string;
    
    // 检查是否配置了API密钥
    if (!config.apiKey) {
      console.warn('AI API密钥未配置，使用本地解读');
      interpretation = generateLocalInterpretation(request);
      
      return {
        interpretation,
        success: true
      };
    }
    
    // 根据配置调用相应的AI服务
    switch (config.provider) {
      case 'openai':
        interpretation = await callOpenAI(prompt, config.apiKey);
        break;
        
      case 'deepseek':
        interpretation = await callDeepSeek(prompt, config.apiKey, config.baseURL!);
        break;
        
      default:
        interpretation = generateLocalInterpretation(request);
    }
    
    return {
      interpretation,
      success: true
    };
    
  } catch (error) {
    console.error('AI解读失败:', error);
    
    // 如果AI调用失败，回退到本地解读
    const fallbackInterpretation = generateLocalInterpretation(request);
    
    return {
      interpretation: fallbackInterpretation,
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 检查AI服务状态
 */
export async function checkAIServiceStatus(): Promise<{
  available: boolean;
  provider: string;
  error?: string;
}> {
  const config = getCurrentAIConfig();
  
  if (!config.apiKey) {
    return {
      available: false,
      provider: config.provider,
      error: 'API密钥未配置'
    };
  }
  
  try {
    // 发送一个简单的测试请求
    const testPrompt = '请简单回答：你好';
    
    switch (config.provider) {
      case 'openai':
        await callOpenAI(testPrompt, config.apiKey);
        break;
      case 'deepseek':
        await callDeepSeek(testPrompt, config.apiKey, config.baseURL!);
        break;
    }
    
    return {
      available: true,
      provider: config.provider
    };
    
  } catch (error) {
    return {
      available: false,
      provider: config.provider,
      error: error instanceof Error ? error.message : '连接失败'
    };
  }
}
