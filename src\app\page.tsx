'use client';

import { useState } from 'react';
import { quickDemo } from '@/utils/tarot-demo';

// 简单的文本格式化函数
function formatText(text: string) {
  // 按行分割文本
  const lines = text.split('\n');

  return lines.map((line, lineIndex) => {
    // 处理标题
    if (line.startsWith('## ')) {
      const title = line.slice(3); // 移除 "## "
      return (
        <h2 key={lineIndex} className="text-xl font-bold text-yellow-300 mb-4 mt-6 flex items-center">
          {formatInlineText(title)}
        </h2>
      );
    }

    if (line.startsWith('### ')) {
      const title = line.slice(4); // 移除 "### "
      return (
        <h3 key={lineIndex} className="text-lg font-semibold text-yellow-200 mb-3 mt-4">
          {formatInlineText(title)}
        </h3>
      );
    }

    // 处理普通段落
    if (line.trim() === '') {
      return <br key={lineIndex} />;
    }

    return (
      <p key={lineIndex} className="text-gray-200 leading-relaxed mb-4">
        {formatInlineText(line)}
      </p>
    );
  });
}

// 处理行内格式（粗体等）
function formatInlineText(text: string) {
  const parts = text.split(/(\*\*[^*]+\*\*)/g);

  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      const content = part.slice(2, -2);
      return (
        <strong key={index} className="text-yellow-200 font-bold">
          {content}
        </strong>
      );
    }
    return part;
  });
}

export default function HomePage() {
  const [currentStep, setCurrentStep] = useState<'select' | 'shuffle' | 'draw' | 'result'>('select');
  const [selectedType, setSelectedType] = useState<'single' | 'love' | 'career' | 'future' | null>(null);
  const [question, setQuestion] = useState('');
  const [shuffling, setShuffling] = useState(false);
  const [drawnCards, setDrawnCards] = useState<any[]>([]);
  const [flippedCards, setFlippedCards] = useState<boolean[]>([]);
  const [currentReading, setCurrentReading] = useState<any>(null);
  const [showInstructions, setShowInstructions] = useState(true);

  const startReading = (type: 'single' | 'love' | 'career' | 'future') => {
    setSelectedType(type);
    setShowInstructions(false);
    
    let questionText = '';
    switch (type) {
      case 'single':
        questionText = '今天我需要关注什么？';
        break;
      case 'love':
        questionText = '我的感情运势如何？';
        break;
      case 'career':
        questionText = '我的事业发展方向？';
        break;
      case 'future':
        questionText = '我的过去、现在、未来';
        break;
    }
    setQuestion(questionText);
    setCurrentStep('shuffle');
  };

  const startShuffle = async () => {
    setShuffling(true);
    
    // 模拟洗牌过程
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    setShuffling(false);
    setCurrentStep('draw');
    
    // 准备牌数
    const cardCount = selectedType === 'future' ? 3 : 1;
    setDrawnCards(new Array(cardCount).fill(null));
    setFlippedCards(new Array(cardCount).fill(false));
  };

  const drawCard = async (index: number) => {
    try {
      let result;
      
      if (selectedType === 'future' && index === 0) {
        // 如果是三张牌占卜，一次性抽取所有牌
        result = quickDemo.threeCardReading(question);
        setDrawnCards(result.cards);
      } else if (selectedType !== 'future') {
        // 单张牌占卜
        result = quickDemo.quickReading(question);
        setDrawnCards([{ name: result.card, orientation: result.orientation }]);
      }
      
      // 延迟显示，模拟翻牌动画
      setTimeout(() => {
        const newFlipped = [...flippedCards];
        if (selectedType === 'future') {
          // 三张牌依次翻开
          newFlipped[index] = true;
          setFlippedCards(newFlipped);
          
          if (index === 2) {
            // 最后一张牌翻开后，显示解读
            setTimeout(() => {
              setCurrentReading({ ...result, question, type: selectedType });
              setCurrentStep('result');
            }, 1000);
          }
        } else {
          // 单张牌
          newFlipped[0] = true;
          setFlippedCards(newFlipped);
          setTimeout(() => {
            setCurrentReading({ ...result, question, type: selectedType });
            setCurrentStep('result');
          }, 1000);
        }
      }, 500);
      
    } catch (error) {
      console.error('抽牌出错：', error);
    }
  };

  const resetReading = () => {
    setCurrentStep('select');
    setSelectedType(null);
    setQuestion('');
    setDrawnCards([]);
    setFlippedCards([]);
    setCurrentReading(null);
    setShowInstructions(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 text-white overflow-hidden">
      {/* 星空背景 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-1 h-1 bg-white rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-yellow-200 rounded-full animate-pulse"></div>
        <div className="absolute top-60 left-1/3 w-1 h-1 bg-blue-200 rounded-full animate-pulse"></div>
        <div className="absolute top-80 right-1/3 w-1 h-1 bg-purple-200 rounded-full animate-pulse"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        {currentStep !== 'select' && (
          <div className="mb-8">
            <button
              onClick={resetReading}
              className="inline-flex items-center space-x-2 bg-white bg-opacity-10 hover:bg-opacity-20 rounded-full px-6 py-3 transition-all duration-200"
            >
              <span>←</span>
              <span>重新开始</span>
            </button>
          </div>
        )}

        {currentStep === 'select' && (
          /* 1. 选择占卜类型 */
          <>
            {/* 头部 */}
            <div className="text-center mb-16">
              <div className="inline-block mb-6">
                <div className="text-8xl mb-4 animate-bounce">🔮</div>
                <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent">
                  神秘塔罗牌
                </h1>
                <p className="text-2xl text-gray-300 mb-6">
                  探索内心的智慧，窥见命运的奥秘
                </p>
              </div>
              
              {showInstructions && (
                <div className="max-w-2xl mx-auto bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 mb-12">
                  <h2 className="text-2xl font-semibold mb-4 text-yellow-300">✨ 如何开始</h2>
                  <div className="text-left space-y-3 text-gray-200">
                    <p className="flex items-center"><span className="mr-3">🎯</span>选择你想要了解的方面</p>
                    <p className="flex items-center"><span className="mr-3">🔀</span>观看神秘的洗牌过程</p>
                    <p className="flex items-center"><span className="mr-3">👆</span>亲手抽取命运的塔罗牌</p>
                    <p className="flex items-center"><span className="mr-3">📖</span>获得专业的解读和指引</p>
                  </div>
                </div>
              )}
            </div>

            {/* 占卜选择 */}
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-12 text-yellow-300">选择你的占卜</h2>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {/* 今日运势 */}
                <div className="group cursor-pointer" onClick={() => startReading('single')}>
                  <div className="bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div className="text-6xl mb-4 text-center group-hover:animate-bounce">☀️</div>
                    <h3 className="text-2xl font-bold mb-3 text-center">今日运势</h3>
                    <p className="text-gray-200 text-center mb-4">了解今天需要关注的重点</p>
                    <div className="text-center">
                      <span className="inline-block px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm">
                        单张牌占卜
                      </span>
                    </div>
                  </div>
                </div>

                {/* 爱情运势 */}
                <div className="group cursor-pointer" onClick={() => startReading('love')}>
                  <div className="bg-gradient-to-br from-pink-600 to-red-600 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div className="text-6xl mb-4 text-center group-hover:animate-bounce">💕</div>
                    <h3 className="text-2xl font-bold mb-3 text-center">爱情运势</h3>
                    <p className="text-gray-200 text-center mb-4">探索你的感情世界</p>
                    <div className="text-center">
                      <span className="inline-block px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm">
                        情感专属
                      </span>
                    </div>
                  </div>
                </div>

                {/* 事业财运 */}
                <div className="group cursor-pointer" onClick={() => startReading('career')}>
                  <div className="bg-gradient-to-br from-green-600 to-blue-600 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div className="text-6xl mb-4 text-center group-hover:animate-bounce">💼</div>
                    <h3 className="text-2xl font-bold mb-3 text-center">事业财运</h3>
                    <p className="text-gray-200 text-center mb-4">指引你的发展方向</p>
                    <div className="text-center">
                      <span className="inline-block px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm">
                        前程指导
                      </span>
                    </div>
                  </div>
                </div>

                {/* 时光之轮 */}
                <div className="group cursor-pointer" onClick={() => startReading('future')}>
                  <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div className="text-6xl mb-4 text-center group-hover:animate-bounce">⏳</div>
                    <h3 className="text-2xl font-bold mb-3 text-center">时光之轮</h3>
                    <p className="text-gray-200 text-center mb-4">窥见过去现在未来</p>
                    <div className="text-center">
                      <span className="inline-block px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm">
                        三张牌占卜
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {currentStep === 'shuffle' && (
          /* 2. 洗牌环节 */
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-12">
              <h2 className="text-4xl font-bold mb-4 text-yellow-300">{question}</h2>
              <p className="text-gray-300">请专注于你的问题，让我们开始洗牌...</p>
            </div>

            <div className="bg-white bg-opacity-10 rounded-2xl p-12 backdrop-blur-sm border border-white border-opacity-20 mb-8">
              <div className="text-8xl mb-6 animate-pulse">🎴</div>
              <h3 className="text-2xl font-bold mb-6">正在洗牌中...</h3>
              <div className="flex justify-center space-x-2 mb-6">
                <div className="w-3 h-3 bg-yellow-400 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
                <div className="w-3 h-3 bg-yellow-400 rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
                <div className="w-3 h-3 bg-yellow-400 rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
              </div>
              <p className="text-gray-300">正在将你的能量注入塔罗牌中...</p>
            </div>

            {!shuffling && (
              <button
                onClick={startShuffle}
                className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 px-8 py-4 rounded-2xl font-bold text-xl transform transition-all duration-200 hover:scale-105"
              >
                🔀 开始洗牌
              </button>
            )}
          </div>
        )}

        {currentStep === 'draw' && (
          /* 3. 抽牌环节 */
          <div className="max-w-4xl mx-auto text-center">
            <div className="mb-12">
              <h2 className="text-4xl font-bold mb-4 text-yellow-300">{question}</h2>
              <p className="text-gray-300">
                {selectedType === 'future' ? '请依次点击三张牌，了解你的过去、现在、未来' : '请点击下面的牌来揭示答案'}
              </p>
            </div>

            <div className={`flex justify-center ${selectedType === 'future' ? 'space-x-8' : ''} mb-8`}>
              {drawnCards.map((card, index) => (
                <div key={index} className="text-center">
                  {selectedType === 'future' && (
                    <h4 className="text-lg font-semibold mb-4 text-yellow-300">
                      {['过去', '现在', '未来'][index]}
                    </h4>
                  )}
                  
                  <div
                    className="cursor-pointer transform transition-all duration-500 hover:scale-105"
                    onClick={() => !flippedCards[index] && drawCard(index)}
                  >
                    {!flippedCards[index] ? (
                      /* 牌背 */
                      <div className="w-32 h-48 bg-gradient-to-br from-purple-800 to-indigo-900 rounded-2xl border-4 border-yellow-400 shadow-2xl flex items-center justify-center relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-purple-600/20"></div>
                        <div className="relative z-10">
                          <div className="text-4xl mb-2">🔮</div>
                          <div className="text-sm text-yellow-300 font-bold">TAROT</div>
                        </div>
                        <div className="absolute inset-0 border-2 border-yellow-300 rounded-2xl opacity-50"></div>
                      </div>
                    ) : (
                      /* 翻开的牌 */
                      <div className="w-32 h-48 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-2xl border-4 border-yellow-300 shadow-2xl flex flex-col items-center justify-center p-4 animate-pulse">
                        <div className="text-3xl mb-2">🎴</div>
                        <div className="text-sm font-bold text-center text-white">
                          {card?.name || drawnCards[index]?.name}
                        </div>
                        <div className={`text-xs mt-2 px-2 py-1 rounded-full ${
                          (card?.orientation || drawnCards[index]?.orientation) === '正位' 
                            ? 'bg-green-500 text-white'
                            : 'bg-red-500 text-white'
                        }`}>
                          {card?.orientation || drawnCards[index]?.orientation}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-yellow-500 bg-opacity-20 rounded-2xl px-6 py-4 inline-block">
              <p className="text-yellow-300 font-semibold">
                👆 点击牌背来抽取你的塔罗牌
              </p>
            </div>
          </div>
        )}

        {currentStep === 'result' && (
          /* 4. 结果展示 */
          <div className="max-w-4xl mx-auto">
            {currentReading?.error ? (
              <div className="text-center">
                <div className="text-6xl mb-4">😢</div>
                <h2 className="text-2xl font-bold mb-4">占卜遇到问题</h2>
                <p className="text-gray-300">{currentReading.error}</p>
              </div>
            ) : (
              <>
                {/* 问题 */}
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold mb-4 text-yellow-300">{question}</h2>
                </div>

                {/* 牌面展示 */}
                <div className="mb-12">
                  {currentReading?.card ? (
                    // 单张牌
                    <div className="text-center">
                      <div className="inline-block bg-gradient-to-br from-yellow-400 to-orange-600 rounded-2xl p-8 mb-8 shadow-2xl transform hover:scale-105 transition-all duration-300">
                        <div className="text-8xl mb-4">🎴</div>
                        <h3 className="text-3xl font-bold text-white mb-2">{currentReading.card}</h3>
                        <span className={`inline-block px-4 py-2 rounded-full text-lg font-semibold ${
                          currentReading.orientation === '正位' 
                            ? 'bg-green-500 text-white'
                            : 'bg-yellow-500 text-black'
                        }`}>
                          {currentReading.orientation}
                        </span>
                      </div>
                    </div>
                  ) : (
                    // 三张牌
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                      {currentReading?.cards?.map((card: any, index: number) => (
                        <div key={index} className="text-center">
                          <div className="bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl p-6 mb-4 shadow-2xl transform hover:scale-105 transition-all duration-300">
                            <div className="text-6xl mb-3">🎴</div>
                            <h4 className="text-lg font-semibold text-gray-200 mb-2">
                              {['过去', '现在', '未来'][index]}
                            </h4>
                            <h3 className="text-xl font-bold text-white mb-2">{card.name}</h3>
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${
                              card.orientation === '正位' 
                                ? 'bg-green-500 text-white'
                                : 'bg-yellow-500 text-black'
                            }`}>
                              {card.orientation}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* 解读内容 */}
                <div className="bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20">
                  <h3 className="text-2xl font-bold mb-6 text-yellow-300 flex items-center">
                    <span className="mr-3">📜</span>
                    塔罗解读
                  </h3>
                  <div className="text-gray-200 leading-relaxed whitespace-pre-line">
                    {formatText(currentReading?.interpretation || '')}
                  </div>
                </div>

                {/* 重新占卜按钮 */}
                <div className="text-center mt-12">
                  <button
                    onClick={resetReading}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 px-8 py-4 rounded-2xl font-bold text-xl transform transition-all duration-200 hover:scale-105 mr-4"
                  >
                    🔮 再次占卜
                  </button>
                </div>

                {/* 底部提示 */}
                <div className="text-center mt-8">
                  <div className="inline-block bg-yellow-500 bg-opacity-20 rounded-2xl px-6 py-4">
                    <p className="text-yellow-300 font-semibold">
                      ✨ 塔罗牌提供的是指引，最终的选择权在你手中
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
