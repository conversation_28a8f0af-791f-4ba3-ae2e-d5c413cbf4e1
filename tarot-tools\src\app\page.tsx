'use client';

import { useState, useEffect } from 'react';
import TarotCard from '@/components/TarotCard';
import SpreadLayout from '@/components/SpreadLayouts';
import { getAIInterpretation, type TarotCard as AITarotCard } from '@/lib/ai-interpreter';
import { MAJOR_ARCANA, getAllCards } from '@/constants/tarot-cards';

// 简单的文本格式化函数
function formatText(text: string) {
  // 按行分割文本
  const lines = text.split('\n');

  return lines.map((line, lineIndex) => {
    // 处理标题
    if (line.startsWith('## ')) {
      const title = line.slice(3); // 移除 "## "
      return (
        <h2 key={lineIndex} className="text-xl font-bold text-amber-300 mb-4 mt-6 flex items-center">
          {formatInlineText(title)}
        </h2>
      );
    }

    if (line.startsWith('### ')) {
      const title = line.slice(4); // 移除 "### "
      return (
        <h3 key={lineIndex} className="text-lg font-semibold text-amber-200 mb-3 mt-4">
          {formatInlineText(title)}
        </h3>
      );
    }

    // 处理普通段落
    if (line.trim() === '') {
      return <br key={lineIndex} />;
    }

    return (
      <p key={lineIndex} className="text-purple-100 leading-relaxed mb-4">
        {formatInlineText(line)}
      </p>
    );
  });
}

// 处理行内格式（粗体等）
function formatInlineText(text: string) {
  const parts = text.split(/(\*\*[^*]+\*\*)/g);

  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      const content = part.slice(2, -2);
      return (
        <strong key={index} className="text-amber-200 font-bold">
          {content}
        </strong>
      );
    }
    return part;
  });
}

export default function Home() {
  const [currentStep, setCurrentStep] = useState<'select' | 'question' | 'spread' | 'shuffle' | 'draw' | 'interpreting' | 'result' | 'preview' | 'cardback'>('select');
  const [selectedType, setSelectedType] = useState<'single' | 'three' | 'cross' | 'celtic' | 'love' | 'career' | 'horseshoe' | 'star' | 'pyramid' | 'moon' | 'chakra' | 'decision' | null>(null);
  const [question, setQuestion] = useState('');
  const [shuffling, setShuffling] = useState(false);
  const [deckCards, setDeckCards] = useState<number[]>([]);
  const [selectedCards, setSelectedCards] = useState<number[]>([]);
  const [revealedCards, setRevealedCards] = useState<any[]>([]);
  const [maxCards, setMaxCards] = useState(1);
  const [currentReading, setCurrentReading] = useState<any>(null);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [interpretingProgress, setInterpretingProgress] = useState(0);
  const [cardLayout, setCardLayout] = useState<'circle' | 'grid' | 'spiral' | 'arc'>('circle');
  const [allCardsFlipped, setAllCardsFlipped] = useState(false);
  const [flippedCards, setFlippedCards] = useState<Set<string>>(new Set());
  const [selectedCardback, setSelectedCardback] = useState<string>('classic');

  // 加载用户选择的卡背样式
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCardback = localStorage.getItem('selectedCardback');
      if (savedCardback) {
        setSelectedCardback(savedCardback);
      }
    }
  }, []);

  const startReading = (type: 'single' | 'three' | 'cross' | 'celtic' | 'love' | 'career' | 'horseshoe' | 'star' | 'pyramid' | 'moon' | 'chakra' | 'decision') => {
    setSelectedType(type);
    setCurrentStep('question');

    // 设置牌数
    const cardCounts = {
      'single': 1,
      'three': 3,
      'love': 3,
      'career': 3,
      'cross': 5,
      'horseshoe': 7,
      'celtic': 10,
      'star': 7,
      'pyramid': 6,
      'moon': 4,
      'chakra': 7,
      'decision': 5
    };
    setMaxCards(cardCounts[type]);
  };

  const startPreview = () => {
    setCurrentStep('preview');
    setAllCardsFlipped(false);
    setFlippedCards(new Set());
  };

  const startCardbackSelection = () => {
    setCurrentStep('cardback');
  };

  const selectCardback = (style: string) => {
    setSelectedCardback(style);
    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedCardback', style);
    }
  };

  // 渲染不同风格的卡背
  const renderCardback = (style: string, isPreview = false, isSmall = false) => {
    const baseClasses = isPreview 
      ? "w-32 h-48 mx-auto" 
      : "w-full h-full";
    
    const symbolSize = isSmall 
      ? (isPreview ? "text-lg" : "text-xs")
      : (isPreview ? "text-xl" : "text-lg");
    
    const decorSize = isSmall 
      ? "text-xs"
      : (isPreview ? "text-sm" : "text-xs");
    
    switch (style) {
      case 'classic':
        return (
          <div className={`${baseClasses} relative bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950 overflow-hidden rounded-lg`}>
            <div className="absolute inset-1 border-2 border-amber-400/40 rounded-lg"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className={`text-amber-300 ${symbolSize}`}>⚜</div>
            </div>
            <div className={`absolute top-2 left-2 text-amber-400/60 ${decorSize}`}>✦</div>
            <div className={`absolute top-2 right-2 text-amber-400/60 ${decorSize}`}>✦</div>
            <div className={`absolute bottom-2 left-2 text-amber-400/60 ${decorSize}`}>✦</div>
            <div className={`absolute bottom-2 right-2 text-amber-400/60 ${decorSize}`}>✦</div>
          </div>
        );
        
      case 'luxury':
        return (
          <div className={`${baseClasses} relative bg-black overflow-hidden rounded-lg`}>
            <div className="absolute inset-0 border-2 border-amber-400/80 rounded-lg"></div>
            <div className="absolute inset-1 border border-amber-400/60 rounded-lg"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative w-12 h-12">
                <div className="absolute inset-0 border-2 border-amber-400/70 rounded-full animate-pulse"></div>
                <div className="absolute inset-2 border border-amber-400/60 rounded-full"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className={`text-amber-400 ${symbolSize}`}>🌟</div>
                </div>
              </div>
            </div>
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-amber-400/80 text-xs tracking-widest">TAROT</div>
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-amber-400/80 text-xs tracking-widest">MYSTICAL</div>
          </div>
        );
        
      case 'sacred':
        return (
          <div className={`${baseClasses} relative bg-gradient-to-br from-slate-900 via-purple-950 to-indigo-950 overflow-hidden rounded-lg`}>
            <div className="absolute inset-0 opacity-30">
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(251,191,36,0.15)_0%,transparent_60%)]"></div>
            </div>
            <div className="absolute inset-1 border border-amber-400/50 rounded-lg"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative w-10 h-10">
                <div className={`absolute inset-0 text-amber-400/80 ${symbolSize} flex items-center justify-center`}>🕎</div>
                <div className="absolute -inset-2 border border-amber-400/30 rounded-full"></div>
              </div>
            </div>
            <div className="absolute top-3 left-3 text-amber-400/60 text-xs">✡</div>
            <div className="absolute top-3 right-3 text-amber-400/60 text-xs">☪</div>
            <div className="absolute bottom-3 left-3 text-amber-400/60 text-xs">☯</div>
            <div className="absolute bottom-3 right-3 text-amber-400/60 text-xs">🔯</div>
          </div>
        );
        
      case 'cosmic':
        return (
          <div className={`${baseClasses} relative bg-gradient-to-br from-indigo-950 via-blue-950 to-purple-950 overflow-hidden rounded-lg`}>
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.1)_0%,transparent_70%)]"></div>
            <div className="absolute inset-1 border border-blue-400/50 rounded-lg"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative">
                <div className={`text-blue-300 ${symbolSize} animate-pulse`}>🌌</div>
              </div>
            </div>
            <div className="absolute top-2 left-2 text-blue-400/60 text-xs">✨</div>
            <div className="absolute top-2 right-2 text-blue-400/60 text-xs">⭐</div>
            <div className="absolute bottom-2 left-2 text-blue-400/60 text-xs">🌙</div>
            <div className="absolute bottom-2 right-2 text-blue-400/60 text-xs">☄️</div>
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-blue-300/80 text-xs tracking-wider">COSMIC</div>
          </div>
        );
        
      case 'elegant':
        return (
          <div className={`${baseClasses} relative bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 overflow-hidden rounded-lg`}>
            <div className="absolute inset-0 border border-gray-300/40 rounded-lg"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative w-8 h-8">
                <div className="absolute inset-0 border border-gray-300/60 rounded-full"></div>
                <div className="absolute inset-1 bg-gradient-to-br from-gray-300/20 to-gray-400/20 rounded-full"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className={`text-gray-300 ${symbolSize}`}>◇</div>
                </div>
              </div>
            </div>
            <div className="absolute top-3 left-3 text-gray-400/50 text-xs">◆</div>
            <div className="absolute top-3 right-3 text-gray-400/50 text-xs">◆</div>
            <div className="absolute bottom-3 left-3 text-gray-400/50 text-xs">◆</div>
            <div className="absolute bottom-3 right-3 text-gray-400/50 text-xs">◆</div>
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-gray-300/70 text-xs tracking-widest">ELEGANT</div>
          </div>
        );
        
      case 'royal':
        return (
          <div className={`${baseClasses} relative bg-gradient-to-br from-purple-900 via-indigo-900 to-purple-900 overflow-hidden rounded-lg`}>
            <div className="absolute inset-0 border-2 border-yellow-400/60 rounded-lg"></div>
            <div className="absolute inset-1 border border-yellow-400/40 rounded-lg"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative">
                <div className={`text-yellow-400 ${symbolSize}`}>👑</div>
              </div>
            </div>
            <div className="absolute top-2 left-2 text-yellow-400/60 text-xs">♠</div>
            <div className="absolute top-2 right-2 text-yellow-400/60 text-xs">♥</div>
            <div className="absolute bottom-2 left-2 text-yellow-400/60 text-xs">♣</div>
            <div className="absolute bottom-2 right-2 text-yellow-400/60 text-xs">♦</div>
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-yellow-400/80 text-xs tracking-widest">ROYAL</div>
          </div>
        );
        
      case 'minimal':
        return (
          <div className={`${baseClasses} relative bg-gradient-to-br from-slate-800 to-slate-900 overflow-hidden rounded-lg`}>
            <div className="absolute inset-2 border border-white/30 rounded"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className={`text-white/80 ${symbolSize}`}>◯</div>
            </div>
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-white/60 text-xs tracking-widest">MINIMAL</div>
          </div>
        );
        
      case 'ai-generated':
        return (
          <div className={`${baseClasses} relative overflow-hidden rounded-lg`}>
            {/* 经典神秘的紫色底色 */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950"></div>
            
            {/* 背景纹理 */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute inset-0 bg-gradient-radial from-amber-400/10 via-transparent to-transparent"></div>
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(251,191,36,0.1)_0%,transparent_50%)]"></div>
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(251,191,36,0.1)_0%,transparent_50%)]"></div>
            </div>
            
            {/* AI生成的金色图标层 */}
            <div 
              className="absolute inset-0 bg-contain bg-center bg-no-repeat"
              style={{ backgroundImage: 'url(/images/cardback-ai.png)' }}
            ></div>
            
            {/* 悬停时的光效 */}
            <div className="absolute inset-0 bg-gradient-to-br from-amber-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </div>
        );
        
      default:
        return renderCardback('classic', isPreview);
    }
  };

  const toggleAllCards = () => {
    const newFlipped = !allCardsFlipped;
    setAllCardsFlipped(newFlipped);
    
    if (newFlipped) {
      // 全部翻到正面
      const allCards = getAllCards();
      setFlippedCards(new Set(allCards.map(card => card.id.toString())));
    } else {
      // 全部翻到反面
      setFlippedCards(new Set());
    }
  };

  const toggleSingleCard = (cardId: string) => {
    const newFlippedCards = new Set(flippedCards);
    if (newFlippedCards.has(cardId)) {
      newFlippedCards.delete(cardId);
    } else {
      newFlippedCards.add(cardId);
    }
    setFlippedCards(newFlippedCards);
    
    // 检查是否所有卡牌都翻开了
    const allCards = getAllCards();
    const allFlipped = newFlippedCards.size === allCards.length;
    setAllCardsFlipped(allFlipped);
  };

  const startShuffle = () => {
    setCurrentStep('shuffle');
    setShuffling(true);

    // 生成牌组 - 使用实际的78张牌
    const allCards = getAllCards();
    const cards = Array.from({ length: allCards.length }, (_, i) => i);
    setDeckCards(cards);

    // 模拟洗牌过程
    setTimeout(() => {
      setShuffling(false);
      setCurrentStep('draw');
    }, 3000);
  };

  const selectCard = (cardIndex: number) => {
    if (selectedCards.includes(cardIndex) || selectedCards.length >= maxCards) return;

    const newSelectedCards = [...selectedCards, cardIndex];
    setSelectedCards(newSelectedCards);

    // 如果选够了牌，立即跳转到解读界面并开始AI解读
    if (newSelectedCards.length === maxCards) {
      setTimeout(() => {
        setCurrentStep('interpreting');
        performReading(newSelectedCards);
      }, 800);
    }
  };

  const performReading = async (cardIndices: number[]) => {
    try {
      // 重置进度
      setInterpretingProgress(0);

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setInterpretingProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90; // 保持在90%，等AI完成后再到100%
          }
          // 确保进度不会超过90%
          const increment = Math.random() * 8 + 2; // 2-10的随机增量
          return Math.min(prev + increment, 90);
        });
      }, 500);

      // 根据选中的牌索引生成塔罗牌数据
      const allCards = getAllCards();
      
      // 加载已上传的图片
      const uploadedImages = typeof window !== 'undefined' 
        ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') 
        : {};
      
      const selectedTarotCards: AITarotCard[] = cardIndices.map((index, position) => {
        // 从完整的78张牌中选择
        const cardData = allCards[index % allCards.length];
        const orientation = Math.random() > 0.5 ? '正位' : '逆位';

        return {
          name: cardData.name,
          imageUrl: uploadedImages[cardData.id] || cardData.imageUrl,
          orientation,
          position: getPositionName(position)
        };
      });

      // 设置显示的牌
      setRevealedCards(selectedTarotCards);

      // 准备AI解读请求
      const spreadTypes = {
        'single': '单张牌占卜',
        'three': '三张牌占卜',
        'love': '爱情三张牌占卜',
        'career': '事业三张牌占卜',
        'cross': '十字牌阵',
        'celtic': '凯尔特十字牌阵',
        'horseshoe': '马蹄铁牌阵',
        'star': '七芒星牌阵',
        'pyramid': '金字塔牌阵',
        'moon': '月相牌阵',
        'chakra': '脉轮牌阵',
        'decision': '决策牌阵'
      };

      const readingTypes = {
        'single': '快速指引',
        'three': '过去现在未来',
        'love': '爱情运势',
        'career': '事业发展',
        'cross': '综合运势',
        'celtic': '深度解读',
        'horseshoe': '七个层面解读',
        'star': '能量流动解读',
        'pyramid': '层次递进解读',
        'moon': '情感周期解读',
        'chakra': '能量中心解读',
        'decision': '选择路径解读'
      };

      const aiRequest = {
        question,
        cards: selectedTarotCards,
        spreadType: spreadTypes[selectedType!] || '塔罗占卜',
        readingType: readingTypes[selectedType!] || '综合解读'
      };

      // 调用AI解读
      const aiResult = await getAIInterpretation(aiRequest);

      // 完成进度并跳转
      setInterpretingProgress(100);

      setTimeout(() => {
        setCurrentReading({
          interpretation: aiResult.interpretation,
          error: aiResult.success ? null : aiResult.error || '解读生成失败'
        });
        setCurrentStep('result');
      }, 500);

    } catch (error) {
      console.error('占卜过程出错:', error);
      setInterpretingProgress(100);

      setTimeout(() => {
        setCurrentReading({
          interpretation: '',
          error: '占卜过程中出现了问题，请重试。'
        });
        setCurrentStep('result');
      }, 500);
    }
  };

  const resetReading = () => {
    setCurrentStep('select');
    setSelectedType(null);
    setQuestion('');
    setDeckCards([]);
    setSelectedCards([]);
    setRevealedCards([]);
    setCurrentReading(null);
    setInterpretingProgress(0);
    setAllCardsFlipped(false);
    setFlippedCards(new Set());
  };

  // 渲染单个卡牌
  const renderCard = (i: number, customStyle?: React.CSSProperties) => {
    const isGrid = cardLayout === 'grid';

    return (
      <div
        key={i}
        onClick={() => selectCard(i)}
        onMouseEnter={() => setHoveredCard(i)}
        onMouseLeave={() => setHoveredCard(null)}
        className={`${isGrid ? 'relative' : 'absolute'} cursor-pointer transform transition-all duration-300 ${
          selectedCards.includes(i)
            ? 'scale-125 z-20'
            : hoveredCard === i
            ? 'scale-110 z-10'
            : 'hover:scale-105'
        } ${
          selectedCards.length >= maxCards && !selectedCards.includes(i)
            ? 'opacity-30 cursor-not-allowed'
            : ''
        }`}
        style={customStyle}
      >
        <div className={`${isGrid ? 'w-[63px] h-[96px]' : 'w-[49px] h-[73px]'} rounded-[6px] border transition-all duration-300 overflow-hidden ${
          selectedCards.includes(i)
            ? 'border-amber-400 shadow-lg shadow-amber-400/30'
            : 'border-amber-400'
        }`}>
          {selectedCards.includes(i) ? (
            // 已选中状态 - 发光效果
            <div className="w-full h-full bg-gradient-to-br from-amber-400/40 to-amber-600/40 flex items-center justify-center">
              <div className={`text-amber-300 ${isGrid ? 'text-lg' : 'text-sm'}`}>✨</div>
            </div>
          ) : (
            // 未选中状态 - 使用用户选择的卡背样式
            <div className="w-full h-full">
              {renderCardback(selectedCardback, false, !isGrid)}
            </div>
          )}
        </div>

        {selectedCards.includes(i) && (
          <div className={`absolute ${isGrid ? '-top-2 -right-2 w-6 h-6' : '-top-1 -right-1 w-5 h-5'} bg-amber-400 rounded-full flex items-center justify-center text-xs font-bold text-purple-900`}>
            {selectedCards.indexOf(i) + 1}
          </div>
        )}
      </div>
    );
  };

  // 渲染不同的卡牌布局
  const renderCardLayout = () => {
    const totalCards = getAllCards().length;

    if (cardLayout === 'circle') {
      // 圆形布局
      return (
        <div className="relative w-full max-w-4xl mx-auto h-96 mb-8">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative w-80 h-80">
              {Array.from({ length: totalCards }, (_, i) => {
                const angleStep = 360 / totalCards;
                const angle = i * angleStep;
                const radius = 130;
                const radian = (angle * Math.PI) / 180;
                const x = Math.cos(radian) * radius;
                const y = Math.sin(radian) * radius;

                return renderCard(i, {
                  left: `calc(50% + ${x}px)`,
                  top: `calc(50% + ${y}px)`,
                  transform: `translate(-50%, -50%) rotate(${angle + 90}deg) ${
                    selectedCards.includes(i) ? 'scale(1.25)' :
                    hoveredCard === i ? 'scale(1.1)' : ''
                  }`,
                  zIndex: selectedCards.includes(i) ? 20 : hoveredCard === i ? 10 : 1
                });
              })}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-gradient-to-br from-purple-900 to-violet-900 rounded-full border-2 border-amber-400/50 flex items-center justify-center">
                <div className="text-3xl animate-pulse">🔮</div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (cardLayout === 'spiral') {
      // 🌀 螺旋布局 - 黄金螺旋展开
      return (
        <div className="relative w-full max-w-6xl mx-auto h-[600px] mb-8">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative w-full h-full">
              {Array.from({ length: totalCards }, (_, i) => {
                const turns = 3; // 螺旋圈数
                const maxRadius = 250;
                const angle = (i / totalCards) * turns * 2 * Math.PI;
                const radius = (i / totalCards) * maxRadius;
                const x = Math.cos(angle) * radius;
                const y = Math.sin(angle) * radius;
                
                return renderCard(i, {
                  left: `calc(50% + ${x}px)`,
                  top: `calc(50% + ${y}px)`,
                  transform: `translate(-50%, -50%) rotate(${(angle * 180 / Math.PI) + 90}deg) ${
                    selectedCards.includes(i) ? 'scale(1.3) translateY(-15px)' :
                    hoveredCard === i ? 'scale(1.15) translateY(-8px)' : ''
                  }`,
                  zIndex: selectedCards.includes(i) ? 200 : hoveredCard === i ? 150 : totalCards - i,
                  filter: selectedCards.includes(i) ? 'drop-shadow(0 15px 30px rgba(251, 191, 36, 0.8))' :
                          hoveredCard === i ? 'drop-shadow(0 8px 20px rgba(251, 191, 36, 0.4))' :
                          'drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3))',
                  transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
                });
              })}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="relative w-20 h-20 bg-gradient-to-br from-amber-400/20 to-purple-900 rounded-full border-2 border-amber-400/60 flex items-center justify-center shadow-2xl">
                  <div className="absolute inset-1 bg-gradient-to-br from-purple-800/50 to-violet-900/50 rounded-full"></div>
                  <div className="relative text-3xl animate-spin" style={{ animationDuration: '8s' }}>🌀</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (cardLayout === 'arc') {
      // 🌙 多层弧形布局
      return (
        <div className="relative w-full max-w-6xl mx-auto h-[500px] mb-8">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative w-full h-full">
              {Array.from({ length: totalCards }, (_, i) => {
                const arcs = 4; // 4层弧形
                const cardsPerArc = Math.ceil(totalCards / arcs);
                const currentArc = Math.floor(i / cardsPerArc);
                const posInArc = i % cardsPerArc;
                const totalInArc = Math.min(cardsPerArc, totalCards - currentArc * cardsPerArc);
                
                const baseRadius = 80 + currentArc * 50;
                const arcAngle = 180; // 每层弧形180度
                const angleStep = arcAngle / Math.max(1, totalInArc - 1);
                const startAngle = -arcAngle / 2;
                const angle = startAngle + (posInArc * angleStep);
                
                const radian = (angle * Math.PI) / 180;
                const x = Math.cos(radian) * baseRadius;
                const y = Math.sin(radian) * baseRadius * 0.6; // 压扁
                
                return renderCard(i, {
                  left: `calc(50% + ${x}px)`,
                  top: `calc(50% + ${y + 100}px)`,
                  transform: `translate(-50%, -50%) rotate(${angle * 0.5}deg) ${
                    selectedCards.includes(i) ? 'scale(1.25) translateY(-12px)' :
                    hoveredCard === i ? 'scale(1.1) translateY(-6px)' : ''
                  }`,
                  zIndex: selectedCards.includes(i) ? 200 : hoveredCard === i ? 150 : arcs - currentArc,
                  filter: selectedCards.includes(i) ? 'drop-shadow(0 12px 25px rgba(251, 191, 36, 0.8))' :
                          hoveredCard === i ? 'drop-shadow(0 6px 15px rgba(251, 191, 36, 0.4))' :
                          'drop-shadow(0 3px 8px rgba(0, 0, 0, 0.2))',
                  transition: 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
                });
              })}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 translate-y-12">
                <div className="relative w-16 h-16 bg-gradient-to-br from-indigo-400/20 to-purple-900 rounded-full border border-indigo-400/60 flex items-center justify-center shadow-2xl">
                  <div className="relative text-2xl animate-pulse">🌙</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // 默认：重叠排列布局 (grid)
      return (
        <div className="relative w-full mb-8">
          <div className="flex overflow-x-auto pb-4 px-8" style={{ 
            scrollbarWidth: 'thin',
            scrollbarColor: '#d97706 transparent'
          }}>
            <div className="flex items-center">
              {Array.from({ length: totalCards }, (_, i) => (
                <div
                  key={i}
                  className="flex-shrink-0"
                  style={{
                    marginLeft: i === 0 ? '0' : '-50px',
                    zIndex: selectedCards.includes(i) ? 100 : totalCards - i,
                  }}
                >
                  {renderCard(i)}
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-center mt-2">
            <div className="text-sm text-purple-300 bg-purple-800/20 px-4 py-2 rounded-full border border-amber-400/20">
              ← 滑动查看所有牌卡 →
            </div>
          </div>
        </div>
      );
    }
  };

  const getSpreadType = (): 'single' | 'three' | 'cross' | 'celtic' => {
    if (selectedType === 'single') return 'single';
    if (selectedType === 'three' || selectedType === 'love' || selectedType === 'career') return 'three';
    if (selectedType === 'cross') return 'cross';
    if (selectedType === 'celtic') return 'celtic';
    return 'single';
  };

  const getPositionName = (index: number): string => {
    const spreadType = getSpreadType();

    if (spreadType === 'single') {
      return '当前指引';
    } else if (spreadType === 'three') {
      return ['过去', '现在', '未来'][index] || `第${index + 1}张牌`;
    }
    return `第${index + 1}张牌`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950 text-white overflow-hidden relative">
      {/* 星空背景 */}
      <div className="absolute inset-0">
        {/* 主要星星 */}
        <div className="absolute top-[10%] left-[15%] w-1 h-1 bg-amber-300 rounded-full animate-pulse shadow-lg shadow-amber-300/50"></div>
        <div className="absolute top-[25%] right-[20%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-1000 shadow-sm shadow-amber-200/30"></div>
        <div className="absolute top-[40%] left-[8%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-500 shadow-lg shadow-amber-400/60"></div>
        <div className="absolute top-[60%] right-[12%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1500"></div>
        <div className="absolute top-[75%] left-[25%] w-1 h-1 bg-amber-200 rounded-full animate-pulse delay-2000 shadow-md shadow-amber-200/40"></div>
        <div className="absolute top-[35%] right-[35%] w-0.5 h-0.5 bg-amber-400 rounded-full animate-pulse delay-700"></div>
        <div className="absolute top-[80%] right-[40%] w-1 h-1 bg-amber-300 rounded-full animate-pulse delay-1200 shadow-lg shadow-amber-300/50"></div>
        <div className="absolute top-[15%] left-[60%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-800"></div>
        <div className="absolute top-[50%] left-[70%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-300 shadow-lg shadow-amber-400/60"></div>
        <div className="absolute top-[90%] left-[45%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1800"></div>
        
        {/* 微小星点 */}
        <div className="absolute top-[20%] left-[30%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-60 animate-pulse delay-2500"></div>
        <div className="absolute top-[70%] right-[60%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-40 animate-pulse delay-3000"></div>
        <div className="absolute top-[45%] left-[85%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-50 animate-pulse delay-2200"></div>
      </div>
      
      {/* 渐变光晕 */}
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-amber-400/5 rounded-full blur-3xl"></div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* 头部导航 */}
        {currentStep !== 'select' && (
          <div className="flex justify-between items-center mb-12">
            <button
              onClick={resetReading}
              className="group flex items-center space-x-3 bg-gradient-to-r from-purple-800/30 to-violet-800/30 hover:from-purple-700/40 hover:to-violet-700/40 backdrop-blur-md rounded-2xl px-8 py-4 transition-all duration-300 border border-amber-400/20 hover:border-amber-400/40 shadow-lg hover:shadow-amber-400/10"
            >
              <span className="text-amber-300 group-hover:text-amber-200 transition-colors text-lg">←</span>
              <span className="text-amber-100 group-hover:text-white transition-colors font-medium">重新开始</span>
            </button>
            
            {/* 进度指示器 */}
            <div className="flex items-center space-x-6">
              <div className="flex space-x-2">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-3 h-3 rounded-full transition-all duration-500 ${
                      i <= ['select', 'question', 'shuffle', 'draw', 'result', 'preview', 'cardback'].indexOf(currentStep)
                        ? 'bg-gradient-to-r from-amber-400 to-amber-300 shadow-lg shadow-amber-400/50'
                        : 'bg-purple-300/20 border border-purple-300/30'
                    }`}
                  ></div>
                ))}
              </div>
              <div className="text-sm text-amber-200 font-medium bg-purple-800/20 px-4 py-2 rounded-full border border-amber-400/20">
                {{'select': '✨ 选择类型', 'question': '💭 输入问题', 'spread': '🔮 准备占卜', 'shuffle': '🌀 洗牌', 'draw': '🎴 选牌', 'result': '📜 解读结果', 'interpreting': '🤖 AI解读中', 'preview': '🎴 预览塔罗牌', 'cardback': '🎨 卡背设计'}[currentStep]}
              </div>
            </div>
            
            <div className="w-32"></div> {/* 占位符保持居中 */}
          </div>
        )}

        {currentStep === 'select' && (
          <div className="max-w-7xl mx-auto">
            {/* 主标题 */}
            <div className="text-center mb-20">
              <div className="relative inline-block mb-12">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-purple-400/20 blur-3xl rounded-full"></div>
                <div className="relative text-8xl mb-6 animate-pulse">🔮</div>
                <div className="absolute -top-2 -right-2 text-2xl animate-spin-slow">✨</div>
                <div className="absolute -bottom-2 -left-2 text-xl animate-bounce">🌙</div>
              </div>

              <h1 className="text-6xl font-bold mb-8 bg-gradient-to-r from-amber-300 via-amber-200 to-amber-100 bg-clip-text text-transparent tracking-wider drop-shadow-lg">
                神秘塔罗
              </h1>
              <p className="text-xl text-purple-200 mb-12 tracking-wide font-light">
                ✨ 探索命运的奥秘 · 窥见未来的真相 ✨
              </p>

              <div className="max-w-5xl mx-auto bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-10 border border-amber-400/20 shadow-2xl shadow-purple-900/20">
                <h2 className="text-2xl font-semibold mb-8 text-amber-300 text-center">✨ 塔罗占卜指引 ✨</h2>
                <div className="grid md:grid-cols-2 gap-8 text-left">
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">🎯</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">专注你的问题</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">在内心深处思考你想要了解的事情，让宇宙感受到你的诚意</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">🌀</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">神秘洗牌仪式</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">观看古老的塔罗牌洗牌过程，感受神秘力量的流动</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">👆</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">凭直觉选牌</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">从78张牌中选择命运为你准备的牌，相信你的第一感觉</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">📜</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">专业解读</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">获得深度的塔罗牌解读和人生指引，照亮前行的道路</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 占卜类型选择 */}
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-16 text-amber-300">🔮 选择你的占卜类型 🔮</h2>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[
                  {
                    type: 'preview',
                    icon: '🎴',
                    title: '预览塔罗牌',
                    desc: '查看78张塔罗牌',
                    tag: '预览模式 · 实用',
                    color: 'from-emerald-400 to-emerald-600',
                    special: true
                  },
                  {
                    type: 'cardback',
                    icon: '🎨',
                    title: '卡背设计',
                    desc: '选择塔罗牌背面',
                    tag: '设计选择 · 个性',
                    color: 'from-rose-400 to-rose-600',
                    special: true
                  },
                  {
                    type: 'single',
                    icon: '🌟',
                    title: '单牌指引',
                    desc: '简单直接的答案',
                    tag: '1张牌 · 初级',
                    color: 'from-amber-400 to-amber-600'
                  },
                  {
                    type: 'three',
                    icon: '⏳',
                    title: '时间三牌',
                    desc: '过去-现在-未来',
                    tag: '3张牌 · 推荐',
                    color: 'from-purple-400 to-purple-600'
                  },
                  {
                    type: 'cross',
                    icon: '✚',
                    title: '十字占卜',
                    desc: '核心-影响-结果',
                    tag: '5张牌 · 中级',
                    color: 'from-violet-400 to-violet-600'
                  },
                  {
                    type: 'celtic',
                    icon: '🎯',
                    title: '凯尔特十字',
                    desc: '最详细的全面解读',
                    tag: '10张牌 · 高级',
                    color: 'from-indigo-400 to-indigo-600'
                  },
                  {
                    type: 'love',
                    icon: '💕',
                    title: '爱情解读',
                    desc: '专门的情感占卜',
                    tag: '3张牌 · 专题',
                    color: 'from-pink-400 to-pink-600'
                  },
                  {
                    type: 'career',
                    icon: '💼',
                    title: '事业指导',
                    desc: '职场发展方向',
                    tag: '3张牌 · 专题',
                    color: 'from-emerald-400 to-emerald-600'
                  },
                  {
                    type: 'horseshoe',
                    icon: '🐎',
                    title: '马蹄铁牌阵',
                    desc: '七个层面的深度分析',
                    tag: '7张牌 · 高级',
                    color: 'from-orange-400 to-orange-600'
                  },
                  {
                    type: 'star',
                    icon: '⭐',
                    title: '七芒星牌阵',
                    desc: '能量流动与平衡',
                    tag: '7张牌 · 神秘',
                    color: 'from-cyan-400 to-cyan-600'
                  },
                  {
                    type: 'pyramid',
                    icon: '🔺',
                    title: '金字塔牌阵',
                    desc: '层次递进的智慧',
                    tag: '6张牌 · 中级',
                    color: 'from-yellow-400 to-yellow-600'
                  },
                  {
                    type: 'moon',
                    icon: '🌙',
                    title: '月相牌阵',
                    desc: '情感周期与直觉',
                    tag: '4张牌 · 灵性',
                    color: 'from-blue-400 to-blue-600'
                  },
                  {
                    type: 'chakra',
                    icon: '🧘',
                    title: '脉轮牌阵',
                    desc: '七大能量中心',
                    tag: '7张牌 · 疗愈',
                    color: 'from-teal-400 to-teal-600'
                  },
                  {
                    type: 'decision',
                    icon: '⚖️',
                    title: '决策牌阵',
                    desc: '选择与后果分析',
                    tag: '5张牌 · 实用',
                    color: 'from-slate-400 to-slate-600'
                  }
                ].map((item) => (
                  <div
                    key={item.type}
                    className="group cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2"
                    onClick={() => {
                      if (item.type === 'preview') {
                        startPreview();
                      } else if (item.type === 'cardback') {
                        startCardbackSelection();
                      } else {
                        startReading(item.type as any);
                      }
                    }}
                  >
                    <div className="relative bg-gradient-to-br from-purple-900/60 to-violet-900/60 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 shadow-2xl overflow-hidden hover:shadow-amber-400/20 transition-all duration-500">
                      {/* 背景光效 */}
                      <div className="absolute inset-0 bg-gradient-to-br from-amber-400/5 to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      
                      {/* 装饰性星星 */}
                      <div className="absolute top-4 right-4 text-amber-300 opacity-60 group-hover:opacity-100 transition-opacity duration-300">✨</div>
                      <div className="absolute bottom-4 left-4 text-amber-300 opacity-40 group-hover:opacity-80 transition-opacity duration-300">🌙</div>
                      
                      <div className="relative z-10">
                        <div className="text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </div>
                        <h3 className="text-xl font-bold mb-3 text-center text-amber-100 group-hover:text-amber-50 transition-colors duration-300">
                          {item.title}
                        </h3>
                        <p className="text-purple-200 text-center mb-6 leading-relaxed text-sm">
                          {item.desc}
                        </p>
                        <div className="text-center">
                          <span className={`inline-block px-4 py-2 bg-gradient-to-r ${item.color} rounded-full text-sm font-semibold text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                            {item.tag}
                          </span>
                        </div>
                      </div>
                      
                      {/* 装饰性边框 */}
                      <div className="absolute inset-0 rounded-3xl border border-amber-400/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {currentStep === 'question' && (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <div className="text-6xl mb-6">💭</div>
              <h2 className="text-4xl font-bold mb-6 text-amber-300">请输入你的问题</h2>
              <p className="text-purple-200 mb-8">让塔罗牌为你揭示答案</p>
            </div>

            <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 mb-8">
              <textarea
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                placeholder="请输入你想要了解的问题..."
                className="w-full h-32 bg-purple-800/30 border border-amber-400/30 rounded-2xl p-4 text-white placeholder-purple-300 focus:outline-none focus:border-amber-400/60 transition-colors resize-none"
              />

              <div className="text-center mt-8">
                <button
                  onClick={startShuffle}
                  disabled={!question.trim()}
                  className="bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 disabled:from-gray-600 disabled:to-gray-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 disabled:scale-100 shadow-lg"
                >
                  开始占卜 ✨
                </button>
              </div>
            </div>

            {/* 牌阵详细信息 - 放在下方 */}
            {(() => {
              const spreadDetails = {
                'single': {
                  icon: '🌟',
                  title: '单牌指引',
                  description: '最简单直接的占卜方式，适合快速获得指引和建议。一张牌能够提供清晰明确的答案，特别适合日常决策和即时问题。',
                  usage: '适用于：日常选择、当下困惑、快速指引、简单问题',
                  examples: '今天我应该做什么？这个决定对我有利吗？我现在的状态如何？',
                  tips: '问题要具体明确，避免过于复杂的多重问题'
                },
                'three': {
                  icon: '⏳',
                  title: '时间三牌',
                  description: '经典的过去-现在-未来牌阵，帮你了解事情的发展脉络。通过三个时间维度，全面分析问题的来龙去脉和发展趋势。',
                  usage: '适用于：了解发展趋势、分析问题根源、预测未来走向、制定计划',
                  examples: '我的感情会如何发展？这个项目的前景怎样？我的职业规划如何？',
                  tips: '适合需要了解事情发展过程的问题，可以是任何领域的问题'
                },
                'cross': {
                  icon: '✚',
                  title: '十字占卜',
                  description: '五张牌组成的十字形牌阵，深入分析问题的核心、影响因素和最终结果。提供全面而平衡的视角。',
                  usage: '适用于：复杂问题分析、多角度思考、寻找解决方案、重要决策',
                  examples: '我应该换工作吗？这段关系值得继续吗？如何解决当前的困境？',
                  tips: '适合需要深入分析的重要问题，会从多个角度给出建议'
                },
                'celtic': {
                  icon: '🎯',
                  title: '凯尔特十字',
                  description: '最经典和详细的塔罗牌阵，十张牌提供最全面的解读。涵盖问题的各个方面，包括潜意识、外在影响、内在力量等。',
                  usage: '适用于：人生重大问题、全面生活分析、深度自我探索、复杂情况',
                  examples: '我的人生方向是什么？如何实现我的目标？我该如何面对人生转折？',
                  tips: '适合最重要的人生问题，需要充足时间来理解和消化解读结果'
                },
                'love': {
                  icon: '💕',
                  title: '爱情解读',
                  description: '专门针对感情问题设计的三牌阵，分析你的感情状态、对方的想法和关系的发展。特别适合情感困惑。',
                  usage: '适用于：恋爱关系、婚姻问题、暗恋困扰、分手复合、感情选择',
                  examples: '他/她对我有感觉吗？我们的关系会有结果吗？我应该表白吗？',
                  tips: '专注于感情相关问题，可以询问具体的感情状况和发展'
                },
                'career': {
                  icon: '💼',
                  title: '事业指导',
                  description: '专门分析职业发展的三牌阵，从当前状况、发展机会和行动建议三个角度指导你的职业规划。',
                  usage: '适用于：职业选择、工作转换、升职加薪、创业决策、职场关系',
                  examples: '我应该跳槽吗？如何在职场上获得成功？这个投资项目可行吗？',
                  tips: '专注于事业和财务相关问题，可以询问具体的职业发展策略'
                },
                'horseshoe': {
                  icon: '🐎',
                  title: '马蹄铁牌阵',
                  description: '七张牌组成的马蹄形牌阵，提供七个层面的深度分析。从过去影响到未来结果，全面解读问题的各个维度。',
                  usage: '适用于：复杂生活问题、多方面分析、长期规划、人际关系、重大变化',
                  examples: '我该如何处理复杂的人际关系？如何平衡工作和生活？人生下一阶段的规划？',
                  tips: '适合需要多角度深入分析的复杂问题，会提供非常详细的指导'
                },
                'star': {
                  icon: '⭐',
                  title: '七芒星牌阵',
                  description: '神秘的七芒星形牌阵，探索精神层面的能量流动。帮助你了解内在力量、精神成长和能量平衡。',
                  usage: '适用于：精神成长、内在探索、能量平衡、灵性问题、创意启发',
                  examples: '我的精神状态如何？如何提升自己的能量？我的天赋是什么？',
                  tips: '适合探索内在世界和精神层面的问题，注重心灵成长和自我认知'
                },
                'pyramid': {
                  icon: '🔺',
                  title: '金字塔牌阵',
                  description: '六张牌组成的金字塔形牌阵，层次递进地揭示问题的深层含义。从基础到顶峰，逐步深入问题核心。',
                  usage: '适用于：目标实现、层次分析、逐步规划、技能提升、个人成长',
                  examples: '如何实现我的目标？我需要提升哪些能力？如何一步步改善现状？',
                  tips: '适合需要分步骤解决的问题，会提供循序渐进的建议和指导'
                },
                'moon': {
                  icon: '🌙',
                  title: '月相牌阵',
                  description: '四张牌对应月亮的四个相位，探索情感周期和直觉指引。特别适合了解情感变化和内在直觉。',
                  usage: '适用于：情感周期、直觉开发、内心声音、情绪管理、女性问题',
                  examples: '我的情绪为什么会波动？如何倾听内心的声音？我的直觉在告诉我什么？',
                  tips: '适合探索情感和直觉相关的问题，特别关注内在感受和情绪变化'
                },
                'chakra': {
                  icon: '🧘',
                  title: '脉轮牌阵',
                  description: '七张牌对应人体七个脉轮能量中心，用于能量疗愈和身心平衡。帮助识别能量阻塞和平衡方法。',
                  usage: '适用于：身心健康、能量疗愈、情绪平衡、精神净化、整体wellness',
                  examples: '我的能量状态如何？哪个方面需要调整？如何保持身心平衡？',
                  tips: '适合关注身心健康和能量平衡的问题，会从整体wellness角度给出建议'
                },
                'decision': {
                  icon: '⚖️',
                  title: '决策牌阵',
                  description: '五张牌专门用于重要决策分析，比较不同选择的利弊和后果。帮助你做出明智的选择。',
                  usage: '适用于：重要选择、利弊分析、风险评估、机会把握、人生转折',
                  examples: '我应该选择A还是B？这个决定的后果是什么？哪个选择对我更有利？',
                  tips: '适合面临重要选择时使用，需要明确说明你在考虑的具体选项'
                }
              };

              const currentSpread = spreadDetails[selectedType!];

              return (
                <div>
                  {/* 牌阵信息卡片 */}
                  <div className="bg-gradient-to-br from-purple-900/60 to-violet-900/60 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20">
                    <div className="flex items-center mb-6">
                      <div className="text-4xl mr-4">{currentSpread.icon}</div>
                      <div>
                        <h3 className="text-2xl font-bold text-amber-300">{currentSpread.title}</h3>
                        <p className="text-purple-200 mt-2">{currentSpread.description}</p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-lg font-semibold text-amber-200 mb-3">📋 适用场景</h4>
                        <p className="text-purple-100 text-sm leading-relaxed">{currentSpread.usage}</p>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-amber-200 mb-3">💡 提问建议</h4>
                        <p className="text-purple-100 text-sm leading-relaxed">{currentSpread.tips}</p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-lg font-semibold text-amber-200 mb-3">🌟 问题示例</h4>
                      <p className="text-purple-100 text-sm leading-relaxed italic">{currentSpread.examples}</p>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        )}

        {currentStep === 'shuffle' && (
          <div className="max-w-4xl mx-auto text-center">
            <div className="text-6xl mb-6">🔮</div>
            <h2 className="text-4xl font-bold mb-6 text-amber-300">正在洗牌...</h2>
            <p className="text-purple-200 mb-8">请静心等待，让宇宙的能量流动</p>

            <div className="relative">
              <div className="grid grid-cols-6 gap-4 mb-8">
                {Array.from({ length: 12 }, (_, i) => (
                  <div
                    key={i}
                    className={`w-[63px] h-[96px] bg-gradient-to-br from-purple-800 to-purple-900 rounded-[6px] border border-amber-400 transform transition-all duration-500 ${
                      shuffling ? 'animate-pulse scale-110 rotate-12' : ''
                    }`}
                    style={{
                      animationDelay: `${i * 100}ms`
                    }}
                  >
                    {/* 使用用户选择的卡背设计 */}
                    <div className="w-full h-full overflow-hidden rounded-lg">
                      {renderCardback(selectedCardback, false)}
                    </div>
                  </div>
                ))}
              </div>

              {shuffling && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-6xl animate-spin">✨</div>
                </div>
              )}
            </div>
          </div>
        )}

        {currentStep === 'draw' && (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <div className="text-6xl mb-6">🎴</div>
              <h2 className="text-4xl font-bold mb-6 text-amber-300">选择你的牌</h2>
              <p className="text-purple-200 mb-4">请凭直觉选择 {maxCards} 张牌</p>
              <p className="text-sm text-purple-300">已选择: {selectedCards.length}/{maxCards}</p>

              {/* 布局切换按钮 */}
              <div className="flex justify-center gap-4 mt-6">
                <button
                  onClick={() => setCardLayout('circle')}
                  className={`px-4 py-2 rounded-lg transition-all duration-300 ${
                    cardLayout === 'circle'
                      ? 'bg-amber-400 text-purple-900 font-bold'
                      : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'
                  }`}
                >
                  🔮 圆形
                </button>
                <button
                  onClick={() => setCardLayout('grid')}
                  className={`px-4 py-2 rounded-lg transition-all duration-300 ${
                    cardLayout === 'grid'
                      ? 'bg-amber-400 text-purple-900 font-bold'
                      : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'
                  }`}
                >
                  📱 网格
                </button>
                <button
                  onClick={() => setCardLayout('spiral')}
                  className={`px-4 py-2 rounded-lg transition-all duration-300 ${
                    cardLayout === 'spiral'
                      ? 'bg-amber-400 text-purple-900 font-bold'
                      : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'
                  }`}
                >
                  🌀 螺旋
                </button>
                <button
                  onClick={() => setCardLayout('arc')}
                  className={`px-4 py-2 rounded-lg transition-all duration-300 ${
                    cardLayout === 'arc'
                      ? 'bg-amber-400 text-purple-900 font-bold'
                      : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'
                  }`}
                >
                  🌙 弧形
                </button>
              </div>
            </div>

            {/* 卡牌排列 */}
            {renderCardLayout()}

            <div className="text-center">
              <button
                onClick={() => setCurrentStep('question')}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors mr-4"
              >
                重新选择问题
              </button>
            </div>
          </div>
        )}

        {currentStep === 'interpreting' && (
          <div className="max-w-4xl mx-auto text-center">
            <div className="text-6xl mb-6">🔮</div>
            <h2 className="text-4xl font-bold mb-6 text-amber-300">神秘力量正在解读...</h2>
            <p className="text-purple-200 mb-8">请稍候，神秘的智慧正在为你揭示答案</p>

            {/* 显示选中的牌 */}
            <div className={`flex flex-wrap justify-center gap-6 mb-8 max-w-6xl mx-auto ${
              revealedCards.length === 1 ? 'justify-center' :
              revealedCards.length <= 3 ? 'justify-center' :
              'justify-center'
            }`}>
              {revealedCards.map((card, index) => (
                <div key={index} className="text-center">
                  <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px]">
                    <h3 className="text-sm font-bold text-amber-300 mb-3">
                      {card.position}
                    </h3>

                    {/* 图片显示区域 */}
                    <div className="w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center">
                      {card.imageUrl ? (
                        <img
                          src={card.imageUrl}
                          alt={card.name}
                          className={`w-full h-full object-cover transition-transform duration-300 scale-110 ${
                            card.orientation === '逆位' ? 'rotate-180' : ''
                          }`}
                          onError={() => {
                            // 图片加载失败时会自动显示备用内容
                          }}
                        />
                      ) : (
                        <div className="text-2xl">🎴</div>
                      )}
                    </div>

                    {/* 牌名和方向显示区域 */}
                    <p className="text-sm font-semibold text-white mb-1">{card.name}</p>
                    <p className="text-xs text-purple-200">{card.orientation}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* 进度条和提示 */}
            <div className="max-w-md mx-auto mb-6">
              <div className="bg-purple-900/40 rounded-full h-2 mb-3 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-amber-400 to-amber-600 h-full rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${interpretingProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-purple-300 mb-2">
                解读进度: {Math.round(interpretingProgress)}%
              </p>

              {/* 动态提示文字 */}
              <div className="text-purple-200 text-sm">
                {interpretingProgress < 30 && "🌟 正在连接宇宙能量..."}
                {interpretingProgress >= 30 && interpretingProgress < 60 && "✨ 分析牌面含义..."}
                {interpretingProgress >= 60 && interpretingProgress < 90 && "🔮 生成专属解读..."}
                {interpretingProgress >= 90 && "💫 即将完成..."}
              </div>
            </div>

            {/* 神秘动画效果 */}
            <div className="flex justify-center space-x-2">
              <div className="w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse" style={{ animationDelay: '0s' }}></div>
              <div className="w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
          </div>
        )}

        {currentStep === 'cardback' && (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <div className="text-6xl mb-6">🎨</div>
              <h2 className="text-4xl font-bold mb-6 text-amber-300">卡背设计选择</h2>
              <p className="text-purple-200 mb-8">选择您喜欢的塔罗牌背面设计风格</p>
            </div>

            {/* 卡背设计选项 */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {[
                {
                  id: 'classic',
                  name: '经典神秘',
                  desc: '传统紫色+金色，神秘符号',
                  preview: '⚜'
                },
                {
                  id: 'luxury',
                  name: '奢华金箔',
                  desc: '黑金配色，双重边框',
                  preview: '🌟'
                },
                {
                  id: 'sacred',
                  name: '神圣几何',
                  desc: '宗教符号，神圣图案',
                  preview: '🕎'
                },
                {
                  id: 'cosmic',
                  name: '宇宙星空',
                  desc: '蓝色星空，天体元素',
                  preview: '🌌'
                },
                {
                  id: 'elegant',
                  name: '优雅简约',
                  desc: '灰色系，简洁几何',
                  preview: '◇'
                },
                {
                  id: 'royal',
                  name: '皇室华丽',
                  desc: '紫金配色，皇室元素',
                  preview: '👑'
                },
                {
                  id: 'minimal',
                  name: '极简现代',
                  desc: '黑白简约，现代美学',
                  preview: '◯'
                },
                {
                  id: 'ai-generated',
                  name: 'AI专业版',
                  desc: '您的AI生成专业卡背',
                  preview: '🤖'
                }
              ].map((style) => (
                <div
                  key={style.id}
                  className={`group cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2 ${
                    selectedCardback === style.id ? 'ring-2 ring-amber-400 ring-offset-2 ring-offset-purple-900' : ''
                  }`}
                  onClick={() => selectCardback(style.id)}
                >
                  <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-6 border border-amber-400/20 shadow-xl">
                    <h3 className="text-xl font-bold text-amber-300 mb-2 text-center">{style.name}</h3>
                    <p className="text-purple-200 text-sm text-center mb-6">{style.desc}</p>
                    
                    {/* 卡背预览 */}
                    <div className="flex justify-center mb-6">
                      {renderCardback(style.id, true)}
                    </div>
                    
                    {/* 选择指示器 */}
                    <div className="text-center">
                      {selectedCardback === style.id ? (
                        <div className="bg-gradient-to-r from-amber-400 to-amber-600 px-4 py-2 rounded-full text-white font-bold text-sm">
                          ✅ 已选择
                        </div>
                      ) : (
                        <div className="bg-purple-800/50 hover:bg-purple-700/50 px-4 py-2 rounded-full text-purple-200 font-medium text-sm transition-colors">
                          点击选择
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* 预览区域 */}
            <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 mb-8">
              <h3 className="text-2xl font-bold text-amber-300 mb-6 text-center">当前选择预览</h3>
              
              <div className="flex justify-center gap-8 items-center">
                <div className="text-center">
                  <h4 className="text-lg font-semibold text-purple-200 mb-4">反面（卡背）</h4>
                  <div className="relative">
                    {renderCardback(selectedCardback, true)}
                  </div>
                </div>
                
                <div className="text-6xl text-amber-300">↔️</div>
                
                <div className="text-center">
                  <h4 className="text-lg font-semibold text-purple-200 mb-4">正面（示例）</h4>
                  <div className="w-32 h-48 mx-auto bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-2xl mb-2">🎴</div>
                      <div className="text-xs text-amber-200">愚人牌</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="text-center mt-6">
                <p className="text-purple-300 text-sm">选择的卡背设计将应用到所有塔罗牌的反面显示</p>
              </div>
            </div>
            
            {/* 操作按钮 */}
            <div className="text-center">
              <button
                onClick={() => {
                  // 保存选择并返回首页
                  if (typeof window !== 'undefined') {
                    localStorage.setItem('selectedCardback', selectedCardback);
                  }
                  resetReading();
                }}
                className="bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg mr-4"
              >
                ✅ 应用选择
              </button>
              
              <button
                onClick={() => startPreview()}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg"
              >
                🎴 预览效果
              </button>
            </div>
          </div>
        )}

        {currentStep === 'preview' && (
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <div className="text-6xl mb-6">🎴</div>
              <h2 className="text-4xl font-bold mb-6 text-amber-300">塔罗牌预览</h2>
              <p className="text-purple-200 mb-8">查看和测试您的78张塔罗牌图片效果</p>
              
              {/* 控制按钮 */}
              <div className="flex justify-center gap-4 mb-8">
                <button
                  onClick={() => {
                    setAllCardsFlipped(false);
                    setFlippedCards(new Set());
                  }}
                  className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg"
                >
                  🔄 全部反面
                </button>
                <button
                  onClick={() => {
                    setAllCardsFlipped(true);
                    const allCards = getAllCards();
                    setFlippedCards(new Set(allCards.map(card => card.id.toString())));
                  }}
                  className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg"
                >
                  ✨ 全部正面
                </button>
                <button
                  onClick={toggleAllCards}
                  className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg"
                >
                  🔀 一键翻转
                </button>
              </div>
            </div>

            {/* 塔罗牌展示区域 - 使用解读界面相同样式 */}
            <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20">
              <div className="flex flex-wrap justify-center gap-6">
                {(() => {
                  const allCards = getAllCards();
                  const uploadedImages = typeof window !== 'undefined' 
                    ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') 
                    : {};

                  return allCards.map((card) => {
                    const isFlipped = allCardsFlipped || flippedCards.has(card.id.toString());
                    const imageUrl = uploadedImages[card.id] || card.imageUrl;
                    
                    return (
                      <div key={card.id} className="text-center">
                        <div 
                          className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px] cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2 group"
                          onClick={() => toggleSingleCard(card.id.toString())}
                        >
                          {/* 卡牌名称标题 */}
                          <h3 className="text-sm font-bold text-amber-300 mb-3">
                            {card.name}
                          </h3>

                          {/* 图片显示区域 - 与解读界面完全相同 */}
                          <div className="w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center relative">
                            {isFlipped ? (
                              // 正面 - 显示卡牌图片
                              imageUrl ? (
                                <img
                                  src={imageUrl}
                                  alt={card.name}
                                  className="w-full h-full object-cover transition-transform duration-300 scale-110"
                                  onError={(e) => {
                                    // 图片加载失败时显示备用内容
                                    e.currentTarget.style.display = 'none';
                                  }}
                                />
                              ) : (
                                <div className="text-2xl">🎴</div>
                              )
                            ) : (
                              // 反面 - 使用用户选择的卡背样式
                              <div className="w-full h-full">
                                {renderCardback(selectedCardback, false)}
                              </div>
                            )}
                            
                            {/* 翻牌指示器 */}
                            <div className="absolute -top-1 -right-1 w-5 h-5 bg-amber-400 rounded-full flex items-center justify-center text-xs font-bold text-purple-900 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              {isFlipped ? '↻' : '↺'}
                            </div>
                          </div>

                          {/* 状态信息 */}
                          <div className="text-center">
                            <p className="text-xs text-purple-200">
                              {isFlipped ? '正面' : '反面'}
                            </p>
                            {imageUrl && isFlipped && (
                              <p className="text-xs text-emerald-300 mt-1">📷 已上传</p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  });
                })()}
              </div>
              
              {/* 统计信息 */}
              <div className="mt-8 text-center">
                <div className="inline-flex items-center space-x-6 bg-purple-800/30 rounded-2xl px-6 py-3 border border-amber-400/20">
                  <div className="text-sm text-purple-200">
                    总计: <span className="text-amber-300 font-bold">78张</span>
                  </div>
                  <div className="text-sm text-purple-200">
                    已翻开: <span className="text-amber-300 font-bold">{flippedCards.size}张</span>
                  </div>
                  <div className="text-sm text-purple-200">
                    有图片: <span className="text-amber-300 font-bold">
                      {(() => {
                        const uploadedImages = typeof window !== 'undefined' 
                          ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') 
                          : {};
                        return Object.keys(uploadedImages).length;
                      })()}张
                    </span>
                  </div>
                </div>
              </div>
              
              {/* 说明文字 */}
              <div className="mt-6 text-center text-sm text-purple-300">
                <p>💡 点击任意卡牌可以单独翻转 · 使用上方按钮批量控制</p>
                <p>📱 已上传的图片会自动显示 · 未上传的显示默认图标</p>
              </div>
            </div>
          </div>
        )}

        {currentStep === 'result' && currentReading && (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <div className="text-6xl mb-6">🔮</div>
              <h2 className="text-4xl font-bold mb-6 text-amber-300">占卜结果</h2>
              <p className="text-purple-200 mb-8">你的问题: "{question}"</p>
            </div>

            {currentReading.error ? (
              <div className="text-center text-red-400 mb-8">
                {currentReading.error}
              </div>
            ) : (
              <>
                <div className={`flex flex-wrap justify-center gap-6 mb-8 max-w-6xl mx-auto ${
                  revealedCards.length === 1 ? 'justify-center' :
                  revealedCards.length <= 3 ? 'justify-center' :
                  'justify-center'
                }`}>
                  {revealedCards.map((card, index) => (
                    <div key={index} className="text-center">
                      <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px]">
                        <h3 className="text-sm font-bold text-amber-300 mb-3">
                          {getPositionName(index)}
                        </h3>

                        {/* 图片显示区域 */}
                        <div className="w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center">
                          {card.imageUrl ? (
                            <img
                              src={card.imageUrl}
                              alt={card.name}
                              className={`w-full h-full object-cover transition-transform duration-300 scale-110 ${
                                card.orientation === '逆位' ? 'rotate-180' : ''
                              }`}
                              onError={() => {
                                // 图片加载失败时会自动显示备用内容
                              }}
                            />
                          ) : (
                            <div className="text-2xl">🎴</div>
                          )}
                        </div>

                        {/* 牌名和方向显示区域 */}
                        <p className="text-sm font-semibold text-white mb-1">{card.name}</p>
                        <p className="text-xs text-purple-200">{card.orientation}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20">
                  <h3 className="text-2xl font-bold text-amber-300 mb-6 text-center">解读</h3>
                  <div className="text-purple-100 leading-relaxed whitespace-pre-line">
                    {formatText(currentReading.interpretation)}
                  </div>
                </div>
              </>
            )}

            <div className="text-center mt-12">
              <button
                onClick={resetReading}
                className="bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg"
              >
                重新占卜 ✨
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
