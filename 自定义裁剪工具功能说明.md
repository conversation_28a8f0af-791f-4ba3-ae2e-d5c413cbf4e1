# 🎨 自定义裁剪工具 - 功能说明

## 📋 功能概览

### 🎯 精确坐标控制
- **X坐标输入框**：设置裁剪框左上角的X坐标位置
- **Y坐标输入框**：设置裁剪框左上角的Y坐标位置
- **实时同步**：拖拽时自动更新坐标值
- **智能限制**：自动约束在有效范围内

### 🔄 圆角实时预览
- **动态显示**：调整圆角时，裁剪框立即显示效果
- **平滑过渡**：0.2秒的过渡动画
- **精确控制**：0-50px的圆角范围

### 📏 尺寸控制
- **自定义宽高**：支持100-5000px范围
- **比例锁定**：保持固定宽高比
- **预设尺寸**：塔罗牌、标准2:3等快捷选项

## 🚀 使用方法

### 1. 基础操作
1. **上传图片**：拖拽或点击选择
2. **设置尺寸**：输入目标宽度和高度
3. **调整位置**：拖拽裁剪框或输入精确坐标
4. **设置圆角**：调整滑块看实时效果
5. **裁剪下载**：点击"裁剪并下载"

### 2. 精确坐标操作
- **手动输入**：在X、Y坐标框中输入具体数值
- **快捷定位**：使用"左上角"、"右上角"等按钮
- **拖拽同步**：拖拽裁剪框时坐标自动更新

### 3. 快捷功能
- **🔄 重置裁剪框**：恢复到中心默认位置
- **📍 居中裁剪框**：移动到图片中心
- **🤖 智能检测边界**：自动识别图片有效区域
- **🎯 精确定位**：一键移动到四个角落

### 4. 高级功能
- **📋 复制设置**：复制所有当前设置（含坐标）
- **💾 保存预设**：保存常用的尺寸和位置组合
- **🚀 批量处理**：使用相同设置处理多张图片

## 📊 技术规格

### 支持格式
- **输入**：JPG、PNG、WebP
- **输出**：JPEG、PNG、WebP
- **质量**：60%-100%可调

### 尺寸限制
- **宽度/高度**：100-5000px
- **坐标范围**：0 到 (图片尺寸-裁剪尺寸)
- **圆角半径**：0-50px

### 预设尺寸
- **塔罗牌**：1030×1795 (约1.74:1)
- **标准2:3**：1080×1620 (1.5:1)
- **4:3比例**：1200×1600 (1.33:1)
- **正方形**：1080×1080 (1:1)

## 🎯 使用技巧

### 精确裁剪
1. 先设置好目标尺寸
2. 使用坐标输入框精确定位
3. 微调圆角达到理想效果
4. 预览确认后下载

### 批量处理
1. 设置好标准参数
2. 点击"批量处理"
3. 选择多张图片
4. 自动应用相同设置

### 效率提升
- 使用**快捷定位**按钮快速移动
- **保存预设**避免重复设置
- **复制设置**在多个项目间共享参数

## 🔧 常见问题

**Q: 为什么坐标输入框有限制？**
A: 系统自动计算有效范围，防止裁剪框超出图片边界

**Q: 圆角显示不准确？**
A: 圆角会根据缩放比例调整，最终效果以预览窗口为准

**Q: 如何实现像素级精确？**
A: 使用坐标输入框输入具体数值，比拖拽更精确

**Q: 批量处理支持不同尺寸吗？**
A: 当前版本使用统一设置，建议按尺寸分组处理

## 💡 最佳实践

1. **准备阶段**：选择高质量原图，确保尺寸足够
2. **设置阶段**：优先设置尺寸，再调整位置
3. **预览阶段**：充分利用实时预览功能
4. **输出阶段**：根据用途选择合适的格式和质量

---

🎴 **专为塔罗牌图片优化，同样适用于所有图片裁剪需求！**
