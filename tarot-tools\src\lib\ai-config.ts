/**
 * AI服务配置
 * 支持OpenAI GPT和DeepSeek API
 */

export interface AIConfig {
  provider: 'openai' | 'deepseek' | 'local';
  apiKey?: string;
  baseURL?: string;
  model: string;
}

// 默认配置
export const AI_CONFIG: AIConfig = {
  provider: 'deepseek', // 可以改为 'openai' 或 'local'
  apiKey: process.env.NEXT_PUBLIC_AI_API_KEY || '', // 在环境变量中设置
  baseURL: process.env.NEXT_PUBLIC_AI_BASE_URL || 'https://api.deepseek.com',
  model: process.env.NEXT_PUBLIC_AI_MODEL || 'deepseek-chat'
};

// OpenAI 配置
export const OPENAI_CONFIG: AIConfig = {
  provider: 'openai',
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
  baseURL: 'https://api.openai.com/v1',
  model: 'gpt-4'
};

// DeepSeek 配置
export const DEEPSEEK_CONFIG: AIConfig = {
  provider: 'deepseek',
  apiKey: process.env.NEXT_PUBLIC_AI_API_KEY || process.env.NEXT_PUBLIC_DEEPSEEK_API_KEY || '',
  baseURL: process.env.NEXT_PUBLIC_AI_BASE_URL || 'https://api.deepseek.com',
  model: process.env.NEXT_PUBLIC_AI_MODEL || 'deepseek-chat'
};

/**
 * 获取当前使用的AI配置
 */
export function getCurrentAIConfig(): AIConfig {
  const provider = process.env.NEXT_PUBLIC_AI_PROVIDER || 'deepseek';
  
  switch (provider) {
    case 'openai':
      return OPENAI_CONFIG;
    case 'deepseek':
      return DEEPSEEK_CONFIG;
    default:
      return AI_CONFIG;
  }
}
