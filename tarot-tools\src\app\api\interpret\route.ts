/**
 * AI塔罗牌解读API路由
 */

import { NextRequest, NextResponse } from 'next/server';

// 直接在API路由中实现AI调用，避免CORS问题
interface AIInterpretationRequest {
  question: string;
  cards: Array<{
    name: string;
    orientation: '正位' | '逆位';
    position?: string;
  }>;
  spreadType: string;
  readingType: string;
}

// 构建塔罗牌解读的Prompt
function buildTarotPrompt(request: AIInterpretationRequest): string {
  const { question, cards, spreadType, readingType } = request;
  
  const cardsDescription = cards.map((card, index) => {
    const position = card.position || `第${index + 1}张牌`;
    return `${position}: ${card.name} (${card.orientation})`;
  }).join('\n');

  return `你是一位专业的塔罗牌解读师，擅长用通俗易懂的语言为现代人提供实用的人生指引。

【用户问题】
${question}

【占卜类型】
${readingType}

【使用牌阵】
${spreadType}

【抽到的牌】
${cardsDescription}

【解读要求】
1. **直接回答用户问题** - 基于塔罗牌给出明确的指引和建议
2. **详细解释牌面含义** - 说明每张牌的含义，特别是正逆位的区别
3. **连接问题与牌面** - 解释为什么这些牌能回答用户的问题
4. **提供实用建议** - 给出具体可行的行动指导
5. **语言风格** - 温暖友善，通俗易懂，避免过于玄奥的表达
6. **内容限制** - 只输出以下指定的三个部分，不要添加其他内容

请严格按照以下格式输出，不要添加任何其他部分：

## 🔮 塔罗指引

**对于您的问题"${question}"，塔罗牌的指引是：[直接回答]**

## 📜 牌面解析

[详细解释每张牌的含义和为什么这样解读]

## 💡 行动建议

[具体的行动建议和注意事项]

请开始解读：`;
}

// 本地备用解读
function generateLocalInterpretation(request: AIInterpretationRequest): string {
  const { question, cards } = request;

  return `## 🔮 塔罗指引

**对于您的问题"${question}"，塔罗牌的指引是：**

根据您抽到的牌面，我看到了积极的发展趋势。塔罗牌建议您保持开放的心态，相信自己的直觉和判断力。

## 📜 牌面解析

${cards.map((card, index) => {
  const meanings = {
    '正位': '这张牌呈现正位，表示能量流动顺畅，是积极发展的好兆头',
    '逆位': '这张牌呈现逆位，提示您需要内省和调整，可能存在需要克服的阻碍'
  };

  return `**${card.position || `第${index + 1}张牌`}**: ${card.name} (${card.orientation})

${meanings[card.orientation]}。${card.name}在您的问题中代表着重要的人生主题，需要您特别关注和思考。`;
}).join('\n\n')}

## 💡 行动建议

根据牌面显示，建议您：

1. **保持积极心态** - 相信自己的直觉和判断，每个挑战都是成长的机会
2. **采取实际行动** - 将内心的想法转化为具体的行动计划
3. **保持耐心** - 好的结果需要时间，不要急于求成
4. **倾听内心** - 在做决定时，多听听自己内心真实的声音

*请记住：塔罗牌提供的是指引和启发，最终的决定权始终在您手中。*`;
}

export async function POST(request: NextRequest) {
  try {
    const body: AIInterpretationRequest = await request.json();
    
    // 验证请求数据
    if (!body.question || !body.cards || body.cards.length === 0) {
      return NextResponse.json(
        { error: '请求数据不完整' },
        { status: 400 }
      );
    }

    // 读取环境变量
    const apiKey = process.env.NEXT_PUBLIC_AI_API_KEY;
    const provider = process.env.NEXT_PUBLIC_AI_PROVIDER || 'deepseek';
    const baseURL = process.env.NEXT_PUBLIC_AI_BASE_URL || 'https://api.deepseek.com';
    
    // 如果没有API密钥，使用本地解读
    if (!apiKey) {
      console.log('没有API密钥，使用本地解读');
      return NextResponse.json({
        interpretation: generateLocalInterpretation(body),
        success: true
      });
    }

    try {
      const prompt = buildTarotPrompt(body);
      
      // 调用DeepSeek API
      const response = await fetch(`${baseURL}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 1500,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`DeepSeek API错误: ${response.status} ${response.statusText}`, errorText);
        
        return NextResponse.json({
          interpretation: generateLocalInterpretation(body),
          success: false,
          error: `DeepSeek API错误: ${response.status} ${response.statusText}`
        });
      }

      const data = await response.json();
      const aiInterpretation = data.choices[0]?.message?.content || '解读生成失败';

      return NextResponse.json({
        interpretation: aiInterpretation,
        success: true
      });

    } catch (aiError) {
      console.error('AI调用失败:', aiError);
      return NextResponse.json({
        interpretation: generateLocalInterpretation(body),
        success: false,
        error: `AI调用失败: ${aiError instanceof Error ? aiError.message : '未知错误'}`
      });
    }
    
  } catch (error) {
    console.error('API解读错误:', error);
    
    return NextResponse.json(
      { 
        error: '解读服务暂时不可用',
        success: false
      },
      { status: 500 }
    );
  }
}

// 支持OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
