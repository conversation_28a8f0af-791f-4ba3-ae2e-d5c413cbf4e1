/**
 * 塔罗牌牌阵布局工具
 * 定义各种经典的塔罗牌布局方式
 */

import { SpreadConfig, SpreadType, SpreadPosition, ReadingType } from '@/types/tarot';

/**
 * 所有可用的牌阵配置
 */
export const TAROT_SPREADS: Record<SpreadType, SpreadConfig> = {
  // 单张牌
  [SpreadType.SINGLE_CARD]: {
    type: SpreadType.SINGLE_CARD,
    name: '单张牌',
    description: '抽取一张牌来回答你的问题或了解当前状况',
    cardCount: 1,
    positions: [
      {
        id: 1,
        name: '答案',
        meaning: '针对你问题的直接答案或指引',
        x: 50,
        y: 50,
        rotation: 0
      }
    ],
    suitable: [ReadingType.GENERAL, ReadingType.LOVE, ReadingType.CAREER, ReadingType.HEALTH, ReadingType.FINANCE, ReadingType.SPIRITUAL]
  },

  // 三张牌布局
  [SpreadType.THREE_CARD]: {
    type: SpreadType.THREE_CARD,
    name: '三张牌',
    description: '经典的过去-现在-未来布局，或者情况-行动-结果',
    cardCount: 3,
    positions: [
      {
        id: 1,
        name: '过去/情况',
        meaning: '影响当前状况的过去事件或背景情况',
        x: 25,
        y: 50,
        rotation: 0
      },
      {
        id: 2,
        name: '现在/行动',
        meaning: '当前的状况或建议采取的行动',
        x: 50,
        y: 50,
        rotation: 0
      },
      {
        id: 3,
        name: '未来/结果',
        meaning: '可能的结果或未来的发展方向',
        x: 75,
        y: 50,
        rotation: 0
      }
    ],
    suitable: [ReadingType.GENERAL, ReadingType.LOVE, ReadingType.CAREER, ReadingType.RELATIONSHIP]
  },

  // 十字牌阵
  [SpreadType.CROSS]: {
    type: SpreadType.CROSS,
    name: '十字牌阵',
    description: '探索问题的各个方面：过去、现在、未来、潜意识和建议',
    cardCount: 5,
    positions: [
      {
        id: 1,
        name: '现在',
        meaning: '当前的状况和核心问题',
        x: 50,
        y: 50,
        rotation: 0
      },
      {
        id: 2,
        name: '过去',
        meaning: '过去的影响和根源',
        x: 25,
        y: 50,
        rotation: 0
      },
      {
        id: 3,
        name: '未来',
        meaning: '可能的未来发展',
        x: 75,
        y: 50,
        rotation: 0
      },
      {
        id: 4,
        name: '潜意识',
        meaning: '潜在的因素和内心深处的想法',
        x: 50,
        y: 25,
        rotation: 0
      },
      {
        id: 5,
        name: '建议',
        meaning: '行动建议和指导',
        x: 50,
        y: 75,
        rotation: 0
      }
    ],
    suitable: [ReadingType.GENERAL, ReadingType.LOVE, ReadingType.CAREER, ReadingType.SPIRITUAL]
  },

  // 凯尔特十字
  [SpreadType.CELTIC_CROSS]: {
    type: SpreadType.CELTIC_CROSS,
    name: '凯尔特十字',
    description: '最经典的塔罗牌阵，提供全面深入的洞察',
    cardCount: 10,
    positions: [
      {
        id: 1,
        name: '现状',
        meaning: '当前的状况',
        x: 40,
        y: 50,
        rotation: 0
      },
      {
        id: 2,
        name: '挑战',
        meaning: '面临的挑战或阻碍',
        x: 40,
        y: 50,
        rotation: 90
      },
      {
        id: 3,
        name: '过去',
        meaning: '过去的基础',
        x: 40,
        y: 70,
        rotation: 0
      },
      {
        id: 4,
        name: '未来',
        meaning: '可能的未来',
        x: 40,
        y: 30,
        rotation: 0
      },
      {
        id: 5,
        name: '目标',
        meaning: '意识层面的目标',
        x: 60,
        y: 50,
        rotation: 0
      },
      {
        id: 6,
        name: '潜意识',
        meaning: '潜意识的影响',
        x: 20,
        y: 50,
        rotation: 0
      },
      {
        id: 7,
        name: '你的方法',
        meaning: '你的态度和方法',
        x: 80,
        y: 85,
        rotation: 0
      },
      {
        id: 8,
        name: '外在影响',
        meaning: '环境和他人的影响',
        x: 80,
        y: 70,
        rotation: 0
      },
      {
        id: 9,
        name: '内在感受',
        meaning: '你的希望和恐惧',
        x: 80,
        y: 55,
        rotation: 0
      },
      {
        id: 10,
        name: '最终结果',
        meaning: '最可能的结果',
        x: 80,
        y: 40,
        rotation: 0
      }
    ],
    suitable: [ReadingType.GENERAL, ReadingType.LOVE, ReadingType.CAREER, ReadingType.SPIRITUAL, ReadingType.RELATIONSHIP]
  },

  // 马蹄形牌阵
  [SpreadType.HORSESHOE]: {
    type: SpreadType.HORSESHOE,
    name: '马蹄形牌阵',
    description: '七张牌的马蹄形布局，探索问题的完整发展过程',
    cardCount: 7,
    positions: [
      {
        id: 1,
        name: '过去',
        meaning: '过去的状况',
        x: 15,
        y: 70,
        rotation: 0
      },
      {
        id: 2,
        name: '现在',
        meaning: '当前的状况',
        x: 25,
        y: 40,
        rotation: 0
      },
      {
        id: 3,
        name: '隐藏因素',
        meaning: '未知的影响因素',
        x: 40,
        y: 25,
        rotation: 0
      },
      {
        id: 4,
        name: '建议',
        meaning: '行动建议',
        x: 60,
        y: 25,
        rotation: 0
      },
      {
        id: 5,
        name: '周围的人',
        meaning: '他人的态度和影响',
        x: 75,
        y: 40,
        rotation: 0
      },
      {
        id: 6,
        name: '行动',
        meaning: '你应该采取的行动',
        x: 85,
        y: 70,
        rotation: 0
      },
      {
        id: 7,
        name: '结果',
        meaning: '可能的结果',
        x: 50,
        y: 85,
        rotation: 0
      }
    ],
    suitable: [ReadingType.GENERAL, ReadingType.CAREER, ReadingType.RELATIONSHIP]
  },

  // 关系牌阵
  [SpreadType.RELATIONSHIP]: {
    type: SpreadType.RELATIONSHIP,
    name: '关系牌阵',
    description: '专门用于分析两人关系的牌阵',
    cardCount: 6,
    positions: [
      {
        id: 1,
        name: '你的感受',
        meaning: '你对这段关系的感受',
        x: 25,
        y: 30,
        rotation: 0
      },
      {
        id: 2,
        name: '对方的感受',
        meaning: '对方对这段关系的感受',
        x: 75,
        y: 30,
        rotation: 0
      },
      {
        id: 3,
        name: '你的需求',
        meaning: '你在关系中的需求',
        x: 25,
        y: 70,
        rotation: 0
      },
      {
        id: 4,
        name: '对方的需求',
        meaning: '对方在关系中的需求',
        x: 75,
        y: 70,
        rotation: 0
      },
      {
        id: 5,
        name: '关系现状',
        meaning: '关系的当前状态',
        x: 50,
        y: 50,
        rotation: 0
      },
      {
        id: 6,
        name: '发展潜力',
        meaning: '关系的发展前景',
        x: 50,
        y: 15,
        rotation: 0
      }
    ],
    suitable: [ReadingType.LOVE, ReadingType.RELATIONSHIP]
  },

  // 年运牌阵
  [SpreadType.YEAR_AHEAD]: {
    type: SpreadType.YEAR_AHEAD,
    name: '年运牌阵',
    description: '十二张牌代表未来十二个月的运势',
    cardCount: 12,
    positions: Array.from({ length: 12 }, (_, i) => {
      const angle = (i * 30) * (Math.PI / 180); // 每月30度
      const radius = 35; // 半径百分比
      const x = 50 + radius * Math.cos(angle - Math.PI / 2);
      const y = 50 + radius * Math.sin(angle - Math.PI / 2);
      
      return {
        id: i + 1,
        name: `${i + 1}月`,
        meaning: `第${i + 1}个月的运势和主要主题`,
        x: Math.round(x),
        y: Math.round(y),
        rotation: 0
      };
    }),
    suitable: [ReadingType.GENERAL, ReadingType.CAREER, ReadingType.FINANCE]
  },

  // 决策牌阵
  [SpreadType.DECISION]: {
    type: SpreadType.DECISION,
    name: '决策牌阵',
    description: '帮助你在两个选择之间做出决定',
    cardCount: 7,
    positions: [
      {
        id: 1,
        name: '当前状况',
        meaning: '你目前的状况',
        x: 50,
        y: 85,
        rotation: 0
      },
      {
        id: 2,
        name: '选择A',
        meaning: '第一个选择的性质',
        x: 25,
        y: 60,
        rotation: 0
      },
      {
        id: 3,
        name: '选择A的结果',
        meaning: '选择A可能带来的结果',
        x: 25,
        y: 35,
        rotation: 0
      },
      {
        id: 4,
        name: '选择B',
        meaning: '第二个选择的性质',
        x: 75,
        y: 60,
        rotation: 0
      },
      {
        id: 5,
        name: '选择B的结果',
        meaning: '选择B可能带来的结果',
        x: 75,
        y: 35,
        rotation: 0
      },
      {
        id: 6,
        name: '潜在因素',
        meaning: '需要考虑的隐藏因素',
        x: 50,
        y: 60,
        rotation: 0
      },
      {
        id: 7,
        name: '最佳指导',
        meaning: '做决定时的最佳指导',
        x: 50,
        y: 15,
        rotation: 0
      }
    ],
    suitable: [ReadingType.GENERAL, ReadingType.CAREER, ReadingType.LOVE]
  }
};

/**
 * 牌阵管理器
 */
export class SpreadManager {
  /**
   * 获取指定类型的牌阵配置
   */
  static getSpread(type: SpreadType): SpreadConfig {
    return TAROT_SPREADS[type];
  }

  /**
   * 获取所有牌阵
   */
  static getAllSpreads(): SpreadConfig[] {
    return Object.values(TAROT_SPREADS);
  }

  /**
   * 根据占卜类型获取适合的牌阵
   */
  static getSpreadsForReading(readingType: ReadingType): SpreadConfig[] {
    return Object.values(TAROT_SPREADS).filter(spread => 
      spread.suitable.includes(readingType)
    );
  }

  /**
   * 根据牌数获取牌阵
   */
  static getSpreadsByCardCount(count: number): SpreadConfig[] {
    return Object.values(TAROT_SPREADS).filter(spread => 
      spread.cardCount === count
    );
  }

  /**
   * 获取推荐的牌阵（根据用户经验级别）
   */
  static getRecommendedSpreads(experience: 'beginner' | 'intermediate' | 'advanced'): SpreadConfig[] {
    switch (experience) {
      case 'beginner':
        return [
          TAROT_SPREADS[SpreadType.SINGLE_CARD],
          TAROT_SPREADS[SpreadType.THREE_CARD]
        ];
      case 'intermediate':
        return [
          TAROT_SPREADS[SpreadType.THREE_CARD],
          TAROT_SPREADS[SpreadType.CROSS],
          TAROT_SPREADS[SpreadType.HORSESHOE]
        ];
      case 'advanced':
        return [
          TAROT_SPREADS[SpreadType.CELTIC_CROSS],
          TAROT_SPREADS[SpreadType.YEAR_AHEAD],
          TAROT_SPREADS[SpreadType.RELATIONSHIP]
        ];
      default:
        return Object.values(TAROT_SPREADS);
    }
  }

  /**
   * 验证牌阵配置
   */
  static validateSpread(spread: SpreadConfig): boolean {
    // 检查牌数与位置数是否匹配
    if (spread.cardCount !== spread.positions.length) {
      return false;
    }

    // 检查位置ID是否唯一且连续
    const ids = spread.positions.map(p => p.id).sort((a, b) => a - b);
    for (let i = 0; i < ids.length; i++) {
      if (ids[i] !== i + 1) {
        return false;
      }
    }

    // 检查坐标是否在有效范围内
    for (const position of spread.positions) {
      if (position.x < 0 || position.x > 100 || position.y < 0 || position.y > 100) {
        return false;
      }
    }

    return true;
  }

  /**
   * 创建自定义牌阵
   */
  static createCustomSpread(
    name: string,
    description: string,
    positions: SpreadPosition[],
    suitable: ReadingType[]
  ): SpreadConfig {
    const spread: SpreadConfig = {
      type: SpreadType.SINGLE_CARD, // 临时类型，实际使用时可能需要扩展enum
      name,
      description,
      cardCount: positions.length,
      positions,
      suitable
    };

    if (!this.validateSpread(spread)) {
      throw new Error('无效的牌阵配置');
    }

    return spread;
  }

  /**
   * 获取牌阵的CSS样式
   */
  static getSpreadStyles(spread: SpreadConfig): Record<number, React.CSSProperties> {
    const styles: Record<number, React.CSSProperties> = {};

    for (const position of spread.positions) {
      styles[position.id] = {
        position: 'absolute',
        left: `${position.x}%`,
        top: `${position.y}%`,
        transform: `translate(-50%, -50%) ${position.rotation ? `rotate(${position.rotation}deg)` : ''}`,
        transition: 'all 0.3s ease'
      };
    }

    return styles;
  }

  /**
   * 计算牌阵的边界框
   */
  static getSpreadBounds(spread: SpreadConfig): {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
    width: number;
    height: number;
  } {
    const xs = spread.positions.map(p => p.x);
    const ys = spread.positions.map(p => p.y);

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      minX,
      maxX,
      minY,
      maxY,
      width: maxX - minX,
      height: maxY - minY
    };
  }
}

/**
 * 便捷的牌阵选择器
 */
export const spreadSelector = {
  /**
   * 为爱情问题选择牌阵
   */
  forLove: () => SpreadManager.getSpreadsForReading(ReadingType.LOVE),

  /**
   * 为事业问题选择牌阵
   */
  forCareer: () => SpreadManager.getSpreadsForReading(ReadingType.CAREER),

  /**
   * 为一般问题选择牌阵
   */
  forGeneral: () => SpreadManager.getSpreadsForReading(ReadingType.GENERAL),

  /**
   * 为初学者选择牌阵
   */
  forBeginner: () => SpreadManager.getRecommendedSpreads('beginner'),

  /**
   * 快速选择 - 根据时间和复杂度
   */
  quick: () => [TAROT_SPREADS[SpreadType.SINGLE_CARD], TAROT_SPREADS[SpreadType.THREE_CARD]],
  
  /**
   * 深度分析
   */
  deep: () => [TAROT_SPREADS[SpreadType.CELTIC_CROSS], TAROT_SPREADS[SpreadType.YEAR_AHEAD]]
};
