# 🌐 HTML塔罗牌裁剪工具使用指南

## 🚀 超简单启动方式

### 方法1：双击打开（推荐）
1. 找到文件：`塔罗牌裁剪工具.html`
2. **双击文件** → 自动在浏览器中打开
3. 开始使用！

### 方法2：浏览器打开
1. 打开任意浏览器（Chrome、Edge、Firefox等）
2. 按 `Ctrl+O` 打开文件
3. 选择 `塔罗牌裁剪工具.html`

## ✨ 功能特点

### 🎯 核心功能
- **精确裁剪**: 固定980×1600像素
- **拖拽调整**: 鼠标拖拽裁剪框位置
- **批量处理**: 支持多文件同时上传
- **实时预览**: 所见即所得
- **自动下载**: 裁剪完成自动保存

### 🔧 操作方式
- **单张精调**: 拖拽裁剪框到理想位置
- **批量处理**: 自动中心裁剪所有图片
- **支持格式**: JPG、PNG、WebP

## 📱 使用步骤

### 步骤1: 打开工具
- 双击 `塔罗牌裁剪工具.html` 文件
- 浏览器会自动打开工具界面

### 步骤2: 上传图片
#### 方式A: 拖拽上传
1. 从文件夹拖拽图片到上传区域
2. 支持多文件同时拖拽

#### 方式B: 点击选择
1. 点击"选择图片文件"按钮
2. 选择一张或多张图片

### 步骤3: 调整裁剪位置
1. **查看预览**: 图片自动加载并显示红色裁剪框
2. **调整位置**: 
   - 拖拽红色裁剪框到理想位置
   - 点击画布任意位置快速定位
   - 点击"重置到中心"回到中心位置

### 步骤4: 保存结果
#### 单张保存
1. 调整裁剪框到满意位置
2. 点击"裁剪并保存"
3. 文件自动下载

#### 批量保存
1. 点击"批量处理全部"
2. 自动处理所有图片（中心裁剪）
3. 文件逐个自动下载

## 🎮 操作技巧

### 裁剪框控制
- **拖拽移动**: 在红色框内点击并拖拽
- **快速定位**: 点击画布任意位置，裁剪框自动移动
- **精确显示**: 实时显示裁剪坐标位置

### 批量处理
- **智能排队**: 文件按顺序自动处理
- **进度显示**: 实时显示处理进度
- **状态跟踪**: 每个文件的处理状态一目了然

### 文件管理
- **自动命名**: 原文件名_980x1600.jpg
- **质量保证**: 90%JPEG质量，兼顾文件大小和清晰度

## 📋 界面介绍

### 主要区域
```
🎴 塔罗牌裁剪工具
├── 📁 上传区域 (拖拽或点击选择)
├── 🖼️ 预览区域 (显示图片和裁剪框)
└── 🎛️ 控制面板
    ├── 📊 图片信息
    ├── 🎯 裁剪控制  
    └── 📂 批量处理
```

### 信息显示
- **文件名**: 当前处理的文件
- **原始尺寸**: 原图片的宽高
- **裁剪位置**: 当前裁剪框的坐标
- **目标尺寸**: 固定980×1600

## 💡 使用建议

### 推荐工作流程
1. **测试单张**: 先上传一张图片，熟悉操作
2. **调整位置**: 拖拽到最佳裁剪位置
3. **批量上传**: 上传所有需要处理的图片
4. **批量处理**: 一键处理所有图片

### 质量优化
- **原图质量**: 建议使用高分辨率原图
- **裁剪位置**: 确保重要内容在裁剪框内
- **批量检查**: 处理完成后抽查几张效果

## ⚠️ 注意事项

### 浏览器要求
- **现代浏览器**: Chrome 60+、Edge 79+、Firefox 60+
- **启用JavaScript**: 工具完全基于JavaScript
- **文件大小**: 建议单个文件不超过50MB

### 图片要求
- **最小尺寸**: 原图应不小于980×1600
- **推荐尺寸**: 1200×2000以上效果更佳
- **文件格式**: JPG、PNG、WebP

### 存储说明
- **本地处理**: 所有处理在浏览器中完成
- **隐私安全**: 图片不会上传到服务器
- **自动下载**: 处理完成的文件保存到默认下载位置

## 🔧 常见问题

### Q: 工具打不开？
A: 确保用现代浏览器打开HTML文件，检查JavaScript是否启用

### Q: 拖拽不起作用？
A: 确保在红色裁剪框内点击并拖拽，或点击画布快速定位

### Q: 批量处理太慢？
A: 大文件处理需要时间，请耐心等待，可以看进度条

### Q: 下载文件在哪里？
A: 默认保存在浏览器设置的下载文件夹中

## 🎯 优势对比

### VS Python工具
- ✅ 无需安装Python环境
- ✅ 双击即可使用
- ✅ 可视化操作界面
- ✅ 跨平台兼容

### VS 在线工具
- ✅ 完全离线使用
- ✅ 隐私安全
- ✅ 无文件大小限制
- ✅ 无网络要求

### VS 专业软件
- ✅ 免费使用
- ✅ 专门为塔罗牌优化
- ✅ 操作简单
- ✅ 批量处理高效

---

**🎴 现在就双击 `塔罗牌裁剪工具.html` 开始使用吧！**
