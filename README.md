# 🔮 塔罗牌工具库

专业的塔罗牌抽牌、布局和解读工具，基于韦特塔罗牌系统开发。

## ✨ 功能特性

- 🎴 **智能抽牌引擎** - 加密级随机算法确保真正的随机性
- 📐 **8种经典牌阵** - 从单张牌到凯尔特十字等专业布局
- 🧠 **智能解读系统** - 基于上下文的专业解读生成
- ⚡ **高性能架构** - 毫秒级响应，支持大量并发
- 🔧 **TypeScript支持** - 完整的类型定义，开发更安全
- 📱 **响应式设计** - 适配各种设备尺寸

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看演示页面。

## 📖 使用指南

### 基础抽牌

```typescript
import { quickDraw } from '@/lib/tarot-draw';

// 抽取单张牌
const singleCard = quickDraw.single(true); // true表示允许逆位

// 抽取三张牌
const threeCards = quickDraw.threeCcard(true);

// 只抽大阿卡纳
const majorCard = quickDraw.majorOnly(1, true);
```

### 使用牌阵

```typescript
import { SpreadManager, TAROT_SPREADS } from '@/lib/tarot-spreads';
import { SpreadType } from '@/types/tarot';

// 获取特定牌阵
const celticCross = SpreadManager.getSpread(SpreadType.CELTIC_CROSS);

// 获取适合爱情占卜的牌阵
const loveSpreads = SpreadManager.getSpreadsForReading(ReadingType.LOVE);

// 为初学者推荐牌阵
const beginnerSpreads = SpreadManager.getRecommendedSpreads('beginner');
```

### 生成解读

```typescript
import { interpretationHelper } from '@/lib/tarot-interpreter';
import { ReadingType, SpreadType } from '@/types/tarot';

// 快速解读
const interpretation = interpretationHelper.quickInterpret(
  drawnCards,
  SpreadType.THREE_CARD,
  ReadingType.LOVE,
  '我的感情发展如何？'
);

// 详细解读
const fullReading = interpretationHelper.detailedInterpret(
  drawnCards,
  SpreadType.CELTIC_CROSS,
  ReadingType.CAREER,
  '我的事业前景如何？'
);
```

### 高级抽牌

```typescript
import { createDrawEngine } from '@/lib/tarot-draw';

const drawEngine = createDrawEngine('user_id');

// 自定义抽牌选项
const cards = drawEngine.drawMultipleCards(5, {
  allowReversed: false,  // 不允许逆位
  shuffleCount: 7,       // 洗牌7次
  excludeCards: [0, 13]  // 排除特定牌
});

// 获取牌组统计
const stats = drawEngine.getDeckStats();

// 验证随机性
const randomnessTest = drawEngine.validateRandomness(1000);
```

## 📚 工具演示

运行内置演示来了解各种功能：

```typescript
import { TarotDemo, quickDemo } from '@/utils/tarot-demo';

// 运行完整演示
TarotDemo.runAllDemos();

// 快速体验
const reading = quickDemo.quickReading('今天的运势如何？');
const threeCardReading = quickDemo.threeCardReading('我的感情走向');
```

## 🎯 支持的牌阵

| 牌阵名称 | 牌数 | 适用场景 | 难度 |
|---------|------|----------|------|
| 单张牌 | 1 | 日常指引、快速解答 | 初级 |
| 三张牌 | 3 | 过去现在未来、情况行动结果 | 初级 |
| 十字牌阵 | 5 | 综合分析、问题探索 | 中级 |
| 马蹄形 | 7 | 完整发展过程 | 中级 |
| 凯尔特十字 | 10 | 深度分析、复杂问题 | 高级 |
| 关系牌阵 | 6 | 人际关系分析 | 中级 |
| 年运牌阵 | 12 | 年度运势预测 | 高级 |
| 决策牌阵 | 7 | 重要决策辅助 | 中级 |

## 🎴 占卜类型

- 💕 **爱情运势** - 感情关系、恋爱发展
- 💼 **事业财运** - 工作发展、财务状况  
- 🌟 **综合运势** - 整体运势、生活指引
- 💪 **健康运势** - 身心健康、生活平衡
- 👥 **人际关系** - 社交关系、人际互动
- 🧘 **精神成长** - 内在发展、灵性提升

## 🏗️ 项目结构

```
src/
├── types/           # TypeScript类型定义
│   └── tarot.ts    # 塔罗牌相关类型
├── constants/       # 数据常量
│   └── tarot-cards.ts  # 78张塔罗牌数据
├── lib/            # 核心工具库
│   ├── tarot-draw.ts      # 抽牌引擎
│   ├── tarot-spreads.ts   # 牌阵管理
│   └── tarot-interpreter.ts # 解读引擎
├── utils/          # 工具函数
│   └── tarot-demo.ts      # 演示和测试
├── components/     # React组件
└── app/           # Next.js页面
    └── page.tsx   # 演示页面
```

## 🔧 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Zustand (预留)
- **部署**: Vercel

## ⚡ 性能特性

- **抽牌性能**: 1000次单牌抽取 < 10ms
- **解读生成**: 100次解读生成 < 100ms
- **内存占用**: 优化的数据结构，内存占用极小
- **并发支持**: 支持多用户同时使用
- **缓存机制**: 智能缓存提升响应速度

## 🧪 测试和验证

项目包含完整的测试套件：

```typescript
import { validateTools, TarotDemo } from '@/utils/tarot-demo';

// 验证所有工具
const isValid = validateTools();

// 性能测试
TarotDemo.performanceTest();

// 随机性验证
const engine = createDrawEngine();
const test = engine.validateRandomness(1000);
```

## 🎨 自定义和扩展

### 创建自定义牌阵

```typescript
import { SpreadManager } from '@/lib/tarot-spreads';

const customSpread = SpreadManager.createCustomSpread(
  '自定义牌阵',
  '描述信息',
  positions,
  [ReadingType.GENERAL]
);
```

### 扩展解读系统

```typescript
import { TarotInterpreter } from '@/lib/tarot-interpreter';

const interpreter = new TarotInterpreter(
  ReadingType.CUSTOM,
  spreadConfig,
  {
    includePositions: true,
    includeReversed: true,
    detailLevel: 'comprehensive',
    readingType: ReadingType.CUSTOM
  }
);
```

## 📈 未来计划

- [ ] 添加更多塔罗牌系统支持
- [ ] AI增强解读功能
- [ ] 用户系统和历史记录
- [ ] 移动端应用
- [ ] 多语言支持
- [ ] 专家系统集成

## 🤝 贡献指南

欢迎提交Issue和Pull Request！请确保：

1. 遵循TypeScript最佳实践
2. 添加适当的类型定义
3. 包含必要的测试
4. 更新相关文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- 基于传统韦特塔罗牌系统
- 参考了多位塔罗专家的解读经验
- 感谢开源社区的技术支持

---

**注意**: 塔罗牌仅供娱乐和自我反思使用，不应作为重大决策的唯一依据。
