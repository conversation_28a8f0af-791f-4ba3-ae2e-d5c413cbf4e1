#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
塔罗牌图片裁剪工具
目标尺寸：980×1600像素
支持手动调整裁剪位置
"""

import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import glob

class TarotCropTool:
    def __init__(self, root):
        self.root = root
        self.root.title("塔罗牌裁剪工具 - 980×1600")
        self.root.geometry("1200x800")
        
        # 裁剪目标尺寸
        self.target_width = 980
        self.target_height = 1600
        
        # 当前图片相关变量
        self.current_image = None
        self.current_image_path = None
        self.display_image = None
        self.scale_factor = 1.0
        self.crop_start_x = 0
        self.crop_start_y = 0
        self.image_files = []
        self.current_index = 0
        
        # 裁剪框变量
        self.crop_rect = None
        self.dragging = False
        self.start_x = 0
        self.start_y = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        ttk.Button(control_frame, text="选择图片文件夹", 
                  command=self.select_folder).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度信息
        self.progress_label = ttk.Label(control_frame, text="请选择图片文件夹")
        self.progress_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # 导航按钮
        nav_frame = ttk.Frame(control_frame)
        nav_frame.pack(side=tk.LEFT)
        
        self.prev_btn = ttk.Button(nav_frame, text="< 上一张", 
                                  command=self.prev_image, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.next_btn = ttk.Button(nav_frame, text="下一张 >", 
                                  command=self.next_image, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 裁剪和保存按钮
        action_frame = ttk.Frame(control_frame)
        action_frame.pack(side=tk.RIGHT)
        
        ttk.Button(action_frame, text="重置裁剪框", 
                  command=self.reset_crop_box).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(action_frame, text="裁剪并保存", 
                  command=self.crop_and_save).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(action_frame, text="批量处理", 
                  command=self.batch_process).pack(side=tk.LEFT)
        
        # 图片显示区域
        self.canvas = tk.Canvas(main_frame, bg='gray', cursor='cross')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, 
                                     text="拖拽鼠标创建980×1600的裁剪框")
        self.status_label.pack(fill=tk.X, pady=(10, 0))
        
    def select_folder(self):
        """选择包含图片的文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含塔罗牌图片的文件夹")
        if folder_path:
            # 支持的图片格式
            extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
            self.image_files = []
            
            for ext in extensions:
                self.image_files.extend(glob.glob(os.path.join(folder_path, ext)))
                self.image_files.extend(glob.glob(os.path.join(folder_path, ext.upper())))
            
            if self.image_files:
                self.image_files.sort()
                self.current_index = 0
                self.load_current_image()
                self.update_navigation()
                self.status_label.config(text=f"找到 {len(self.image_files)} 张图片")
            else:
                messagebox.showwarning("警告", "在选定文件夹中未找到图片文件")
    
    def load_current_image(self):
        """加载当前图片"""
        if not self.image_files:
            return
            
        self.current_image_path = self.image_files[self.current_index]
        
        try:
            # 加载原始图片
            self.current_image = Image.open(self.current_image_path)
            
            # 计算显示尺寸（保持比例，适应画布）
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width <= 1:  # 画布还未初始化
                self.root.after(100, self.load_current_image)
                return
            
            img_width, img_height = self.current_image.size
            
            # 计算缩放比例
            scale_x = (canvas_width - 40) / img_width
            scale_y = (canvas_height - 40) / img_height
            self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
            
            # 创建显示用的图片
            display_width = int(img_width * self.scale_factor)
            display_height = int(img_height * self.scale_factor)
            
            self.display_image = self.current_image.resize(
                (display_width, display_height), Image.Resampling.LANCZOS)
            
            # 转换为PhotoImage
            self.photo = ImageTk.PhotoImage(self.display_image)
            
            # 清空画布并显示图片
            self.canvas.delete("all")
            
            # 居中显示
            x = (canvas_width - display_width) // 2
            y = (canvas_height - display_height) // 2
            
            self.canvas.create_image(x, y, anchor=tk.NW, image=self.photo, tags="image")
            
            # 重置裁剪框
            self.reset_crop_box()
            
            # 更新进度信息
            filename = os.path.basename(self.current_image_path)
            self.progress_label.config(
                text=f"{self.current_index + 1}/{len(self.image_files)}: {filename}")
            
        except Exception as e:
            messagebox.showerror("错误", f"无法加载图片: {str(e)}")
    
    def reset_crop_box(self):
        """重置裁剪框到图片中心"""
        if not self.display_image:
            return
            
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        display_width, display_height = self.display_image.size
        
        # 图片在画布中的位置
        img_x = (canvas_width - display_width) // 2
        img_y = (canvas_height - display_height) // 2
        
        # 计算目标裁剪框在显示图片中的尺寸
        crop_display_width = int(self.target_width * self.scale_factor)
        crop_display_height = int(self.target_height * self.scale_factor)
        
        # 确保裁剪框不超出图片边界
        crop_display_width = min(crop_display_width, display_width)
        crop_display_height = min(crop_display_height, display_height)
        
        # 裁剪框居中
        crop_x = img_x + (display_width - crop_display_width) // 2
        crop_y = img_y + (display_height - crop_display_height) // 2
        
        # 删除旧的裁剪框
        self.canvas.delete("crop_rect")
        
        # 创建新的裁剪框
        self.crop_rect = self.canvas.create_rectangle(
            crop_x, crop_y, 
            crop_x + crop_display_width, crop_y + crop_display_height,
            outline='red', width=2, tags="crop_rect")
        
        # 添加尺寸标签
        self.canvas.create_text(
            crop_x + crop_display_width // 2, crop_y - 10,
            text=f"{self.target_width}×{self.target_height}", 
            fill='red', font=('Arial', 12, 'bold'), tags="crop_rect")
    
    def on_mouse_down(self, event):
        """鼠标按下事件"""
        if self.crop_rect:
            # 检查是否点击在裁剪框内
            coords = self.canvas.coords(self.crop_rect)
            if (coords[0] <= event.x <= coords[2] and 
                coords[1] <= event.y <= coords[3]):
                self.dragging = True
                self.start_x = event.x - coords[0]
                self.start_y = event.y - coords[1]
            else:
                # 创建新的裁剪框
                self.start_x = event.x
                self.start_y = event.y
                self.dragging = True
    
    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.dragging or not self.display_image:
            return
            
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        display_width, display_height = self.display_image.size
        
        # 图片在画布中的位置
        img_x = (canvas_width - display_width) // 2
        img_y = (canvas_height - display_height) // 2
        
        # 计算裁剪框尺寸
        crop_display_width = int(self.target_width * self.scale_factor)
        crop_display_height = int(self.target_height * self.scale_factor)
        
        # 确保裁剪框不超出图片边界
        crop_display_width = min(crop_display_width, display_width)
        crop_display_height = min(crop_display_height, display_height)
        
        # 计算新位置
        new_x = event.x - self.start_x
        new_y = event.y - self.start_y
        
        # 限制在图片范围内
        new_x = max(img_x, min(new_x, img_x + display_width - crop_display_width))
        new_y = max(img_y, min(new_y, img_y + display_height - crop_display_height))
        
        # 更新裁剪框
        self.canvas.delete("crop_rect")
        self.crop_rect = self.canvas.create_rectangle(
            new_x, new_y,
            new_x + crop_display_width, new_y + crop_display_height,
            outline='red', width=2, tags="crop_rect")
        
        # 添加尺寸标签
        self.canvas.create_text(
            new_x + crop_display_width // 2, new_y - 10,
            text=f"{self.target_width}×{self.target_height}", 
            fill='red', font=('Arial', 12, 'bold'), tags="crop_rect")
    
    def on_mouse_up(self, event):
        """鼠标释放事件"""
        self.dragging = False
    
    def crop_and_save(self):
        """裁剪并保存当前图片"""
        if not self.current_image or not self.crop_rect:
            messagebox.showwarning("警告", "请先加载图片并设置裁剪框")
            return
        
        try:
            # 获取裁剪框坐标
            coords = self.canvas.coords(self.crop_rect)
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            display_width, display_height = self.display_image.size
            
            # 图片在画布中的位置
            img_x = (canvas_width - display_width) // 2
            img_y = (canvas_height - display_height) // 2
            
            # 转换为原始图片坐标
            crop_x = (coords[0] - img_x) / self.scale_factor
            crop_y = (coords[1] - img_y) / self.scale_factor
            crop_right = crop_x + self.target_width
            crop_bottom = crop_y + self.target_height
            
            # 确保坐标在有效范围内
            orig_width, orig_height = self.current_image.size
            crop_x = max(0, min(crop_x, orig_width - self.target_width))
            crop_y = max(0, min(crop_y, orig_height - self.target_height))
            crop_right = crop_x + self.target_width
            crop_bottom = crop_y + self.target_height
            
            # 裁剪图片
            cropped = self.current_image.crop((crop_x, crop_y, crop_right, crop_bottom))
            
            # 确保尺寸正确
            if cropped.size != (self.target_width, self.target_height):
                cropped = cropped.resize((self.target_width, self.target_height), 
                                       Image.Resampling.LANCZOS)
            
            # 生成保存路径
            base_dir = os.path.dirname(self.current_image_path)
            output_dir = os.path.join(base_dir, "cropped_980x1600")
            os.makedirs(output_dir, exist_ok=True)
            
            filename = os.path.basename(self.current_image_path)
            name, ext = os.path.splitext(filename)
            output_path = os.path.join(output_dir, f"{name}_cropped{ext}")
            
            # 保存
            cropped.save(output_path, quality=90)
            
            self.status_label.config(text=f"已保存: {output_path}")
            
            # 自动切换到下一张
            if self.current_index < len(self.image_files) - 1:
                self.next_image()
            
        except Exception as e:
            messagebox.showerror("错误", f"裁剪保存失败: {str(e)}")
    
    def batch_process(self):
        """批量处理所有图片（使用中心裁剪）"""
        if not self.image_files:
            messagebox.showwarning("警告", "请先选择图片文件夹")
            return
        
        result = messagebox.askyesno("确认", 
                                   f"将对 {len(self.image_files)} 张图片进行批量中心裁剪\n"
                                   f"目标尺寸: {self.target_width}×{self.target_height}\n"
                                   "是否继续？")
        if not result:
            return
        
        # 创建输出目录
        base_dir = os.path.dirname(self.image_files[0])
        output_dir = os.path.join(base_dir, "batch_cropped_980x1600")
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建进度窗口
        progress_window = tk.Toplevel(self.root)
        progress_window.title("批量处理进度")
        progress_window.geometry("400x100")
        progress_window.transient(self.root)
        progress_window.grab_set()
        
        progress_label = ttk.Label(progress_window, text="准备开始...")
        progress_label.pack(pady=10)
        
        progress_bar = ttk.Progressbar(progress_window, maximum=len(self.image_files))
        progress_bar.pack(pady=10, padx=20, fill=tk.X)
        
        success_count = 0
        
        for i, image_path in enumerate(self.image_files):
            try:
                # 更新进度
                filename = os.path.basename(image_path)
                progress_label.config(text=f"处理: {filename}")
                progress_bar['value'] = i
                progress_window.update()
                
                # 加载图片
                img = Image.open(image_path)
                orig_width, orig_height = img.size
                
                # 中心裁剪
                if orig_width >= self.target_width and orig_height >= self.target_height:
                    # 计算中心裁剪坐标
                    crop_x = (orig_width - self.target_width) // 2
                    crop_y = (orig_height - self.target_height) // 2
                    
                    cropped = img.crop((crop_x, crop_y, 
                                      crop_x + self.target_width, 
                                      crop_y + self.target_height))
                else:
                    # 如果原图太小，先调整大小再裁剪
                    scale = max(self.target_width / orig_width, 
                              self.target_height / orig_height)
                    new_width = int(orig_width * scale)
                    new_height = int(orig_height * scale)
                    
                    resized = img.resize((new_width, new_height), 
                                       Image.Resampling.LANCZOS)
                    
                    crop_x = (new_width - self.target_width) // 2
                    crop_y = (new_height - self.target_height) // 2
                    
                    cropped = resized.crop((crop_x, crop_y,
                                          crop_x + self.target_width,
                                          crop_y + self.target_height))
                
                # 保存
                name, ext = os.path.splitext(filename)
                output_path = os.path.join(output_dir, f"{name}_cropped{ext}")
                cropped.save(output_path, quality=90)
                
                success_count += 1
                
            except Exception as e:
                print(f"处理 {image_path} 时出错: {str(e)}")
        
        progress_window.destroy()
        
        messagebox.showinfo("完成", 
                          f"批量处理完成！\n"
                          f"成功处理: {success_count}/{len(self.image_files)}\n"
                          f"保存位置: {output_dir}")
    
    def prev_image(self):
        """上一张图片"""
        if self.current_index > 0:
            self.current_index -= 1
            self.load_current_image()
            self.update_navigation()
    
    def next_image(self):
        """下一张图片"""
        if self.current_index < len(self.image_files) - 1:
            self.current_index += 1
            self.load_current_image()
            self.update_navigation()
    
    def update_navigation(self):
        """更新导航按钮状态"""
        self.prev_btn.config(state=tk.NORMAL if self.current_index > 0 else tk.DISABLED)
        self.next_btn.config(state=tk.NORMAL if self.current_index < len(self.image_files) - 1 else tk.DISABLED)

def main():
    root = tk.Tk()
    app = TarotCropTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()
