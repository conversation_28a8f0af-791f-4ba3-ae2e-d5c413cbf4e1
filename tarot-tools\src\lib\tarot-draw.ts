/**
 * 塔罗牌抽牌算法工具
 * 提供各种抽牌功能，确保随机性和公平性
 */

import { TarotCard, DrawnCard, TarotOrientation, DrawOptions, DeckConfig, TarotSuit } from '@/types/tarot';
import { getAllCards, getCardsBySuit } from '@/constants/tarot-cards';

/**
 * 加密级随机数生成器
 * 使用多种随机源确保真正的随机性
 */
class SecureRandom {
  private static instance: SecureRandom;
  
  static getInstance(): SecureRandom {
    if (!SecureRandom.instance) {
      SecureRandom.instance = new SecureRandom();
    }
    return SecureRandom.instance;
  }

  /**
   * 生成安全的随机数 (0-1)
   */
  random(): number {
    // 使用多种随机源
    const crypto = typeof window !== 'undefined' ? window.crypto : require('crypto');
    
    if (crypto && crypto.getRandomValues) {
      // 浏览器环境 - 使用 Web Crypto API
      const array = new Uint32Array(1);
      crypto.getRandomValues(array);
      return array[0] / (0xffffffff + 1);
    } else if (crypto && crypto.randomBytes) {
      // Node.js 环境 - 使用 crypto.randomBytes
      const bytes = crypto.randomBytes(4);
      return bytes.readUInt32BE(0) / (0xffffffff + 1);
    } else {
      // 备用方案 - 增强的 Math.random
      return this.enhancedMathRandom();
    }
  }

  /**
   * 增强的 Math.random，结合时间戳和性能计数器
   */
  private enhancedMathRandom(): number {
    const now = Date.now();
    const perf = typeof performance !== 'undefined' ? performance.now() : 0;
    const base = Math.random();
    
    // 使用时间戳和性能计数器作为额外的随机种子
    const seed = (now * perf * base) % 1;
    return (base + seed) % 1;
  }

  /**
   * 生成指定范围内的随机整数
   */
  randomInt(min: number, max: number): number {
    return Math.floor(this.random() * (max - min + 1)) + min;
  }
}

/**
 * 塔罗牌抽牌引擎
 */
export class TarotDrawEngine {
  private secureRandom: SecureRandom;
  private userId?: string;
  
  constructor(userId?: string) {
    this.secureRandom = SecureRandom.getInstance();
    this.userId = userId;
  }

  /**
   * 洗牌算法 - Fisher-Yates 洗牌
   */
  private shuffle<T>(array: T[], times: number = 1): T[] {
    const shuffled = [...array];
    
    for (let round = 0; round < times; round++) {
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = this.secureRandom.randomInt(0, i);
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
    }
    
    return shuffled;
  }

  /**
   * 构建牌组
   */
  private buildDeck(deckConfig: DeckConfig = {
    includeMajorArcana: true,
    includeMinorArcana: true
  }): TarotCard[] {
    let deck: TarotCard[] = [];
    
    if (deckConfig.includeMajorArcana) {
      deck.push(...getCardsBySuit(TarotSuit.MAJOR_ARCANA));
    }
    
    if (deckConfig.includeMinorArcana) {
      const minorSuits = [TarotSuit.WANDS, TarotSuit.CUPS, TarotSuit.SWORDS, TarotSuit.PENTACLES];
      
      for (const suit of minorSuits) {
        if (!deckConfig.excludeSuits?.includes(suit)) {
          deck.push(...getCardsBySuit(suit));
        }
      }
    }
    
    // 排除指定的牌
    if (deckConfig.customCards) {
      deck = deck.filter(card => deckConfig.customCards!.includes(card.id));
    }
    
    return deck;
  }

  /**
   * 决定牌的方向 (正位或逆位)
   */
  private determineOrientation(allowReversed: boolean): TarotOrientation {
    if (!allowReversed) {
      return TarotOrientation.UPRIGHT;
    }
    
    // 30% 概率出现逆位
    return this.secureRandom.random() < 0.3 
      ? TarotOrientation.REVERSED 
      : TarotOrientation.UPRIGHT;
  }

  /**
   * 抽取单张牌
   */
  drawSingleCard(
    options: DrawOptions = { 
      allowReversed: true, 
      shuffleCount: 3 
    },
    deckConfig?: DeckConfig
  ): DrawnCard {
    const deck = this.buildDeck(deckConfig);
    
    // 排除指定的牌
    const availableDeck = options.excludeCards 
      ? deck.filter(card => !options.excludeCards!.includes(card.id))
      : deck;
    
    if (availableDeck.length === 0) {
      throw new Error('没有可用的牌进行抽取');
    }
    
    // 洗牌
    const shuffledDeck = this.shuffle(availableDeck, options.shuffleCount);
    
    // 抽取牌
    const drawnCard = shuffledDeck[0];
    const orientation = this.determineOrientation(options.allowReversed);
    
    return {
      card: drawnCard,
      orientation,
      position: 0
    };
  }

  /**
   * 抽取多张牌
   */
  drawMultipleCards(
    count: number,
    options: DrawOptions = { 
      allowReversed: true, 
      shuffleCount: 3 
    },
    deckConfig?: DeckConfig
  ): DrawnCard[] {
    const deck = this.buildDeck(deckConfig);
    
    // 排除指定的牌
    const availableDeck = options.excludeCards 
      ? deck.filter(card => !options.excludeCards!.includes(card.id))
      : deck;
    
    if (availableDeck.length < count) {
      throw new Error(`牌组数量不足，需要 ${count} 张牌，但只有 ${availableDeck.length} 张可用`);
    }
    
    // 洗牌
    const shuffledDeck = this.shuffle(availableDeck, options.shuffleCount);
    
    // 抽取牌
    const drawnCards: DrawnCard[] = [];
    for (let i = 0; i < count; i++) {
      const orientation = this.determineOrientation(options.allowReversed);
      drawnCards.push({
        card: shuffledDeck[i],
        orientation,
        position: i
      });
    }
    
    return drawnCards;
  }

  /**
   * 抽取不重复的牌
   */
  drawUniqueCards(
    count: number,
    options: DrawOptions = { 
      allowReversed: true, 
      shuffleCount: 3 
    },
    deckConfig?: DeckConfig
  ): DrawnCard[] {
    // 这个方法和 drawMultipleCards 相同，因为我们已经确保不会重复
    return this.drawMultipleCards(count, options, deckConfig);
  }

  /**
   * 重新抽牌 (如果用户不满意)
   */
  redraw(
    previousCards: DrawnCard[],
    options: DrawOptions = { 
      allowReversed: true, 
      shuffleCount: 5 
    },
    deckConfig?: DeckConfig
  ): DrawnCard[] {
    // 排除之前抽到的牌
    const excludeIds = previousCards.map(dc => dc.card.id);
    const newOptions: DrawOptions = {
      ...options,
      excludeCards: [...(options.excludeCards || []), ...excludeIds],
      shuffleCount: Math.max(options.shuffleCount || 3, 5) // 重新抽牌时增加洗牌次数
    };
    
    return this.drawMultipleCards(previousCards.length, newOptions, deckConfig);
  }

  /**
   * 获取牌组统计信息
   */
  getDeckStats(deckConfig?: DeckConfig): {
    totalCards: number;
    majorArcana: number;
    minorArcana: number;
    bySuit: Record<TarotSuit, number>;
  } {
    const deck = this.buildDeck(deckConfig);
    
    const stats = {
      totalCards: deck.length,
      majorArcana: 0,
      minorArcana: 0,
      bySuit: {
        [TarotSuit.MAJOR_ARCANA]: 0,
        [TarotSuit.WANDS]: 0,
        [TarotSuit.CUPS]: 0,
        [TarotSuit.SWORDS]: 0,
        [TarotSuit.PENTACLES]: 0
      }
    };
    
    for (const card of deck) {
      stats.bySuit[card.suit]++;
      
      if (card.suit === TarotSuit.MAJOR_ARCANA) {
        stats.majorArcana++;
      } else {
        stats.minorArcana++;
      }
    }
    
    return stats;
  }

  /**
   * 验证抽牌结果的随机性
   * 用于测试和调试
   */
  validateRandomness(samples: number = 1000): {
    isRandom: boolean;
    distribution: Record<number, number>;
    chiSquare: number;
  } {
    const distribution: Record<number, number> = {};
    const deck = this.buildDeck();
    
    // 进行多次抽牌测试
    for (let i = 0; i < samples; i++) {
      const drawn = this.drawSingleCard();
      const cardId = drawn.card.id;
      distribution[cardId] = (distribution[cardId] || 0) + 1;
    }
    
    // 计算卡方统计量
    const expected = samples / deck.length;
    let chiSquare = 0;
    
    for (const card of deck) {
      const observed = distribution[card.id] || 0;
      chiSquare += Math.pow(observed - expected, 2) / expected;
    }
    
    // 简单的随机性判断 (这是一个基本实现)
    const isRandom = chiSquare < deck.length * 2;
    
    return {
      isRandom,
      distribution,
      chiSquare
    };
  }
}

/**
 * 创建抽牌引擎实例
 */
export const createDrawEngine = (userId?: string): TarotDrawEngine => {
  return new TarotDrawEngine(userId);
};

/**
 * 快速抽牌函数
 */
export const quickDraw = {
  /**
   * 抽取单张牌
   */
  single: (allowReversed: boolean = true): DrawnCard => {
    const engine = createDrawEngine();
    return engine.drawSingleCard({ allowReversed, shuffleCount: 3 });
  },

  /**
   * 抽取三张牌 (过去、现在、未来)
   */
  threeCcard: (allowReversed: boolean = true): DrawnCard[] => {
    const engine = createDrawEngine();
    return engine.drawMultipleCards(3, { allowReversed, shuffleCount: 5 });
  },

  /**
   * 抽取指定数量的牌
   */
  multiple: (count: number, allowReversed: boolean = true): DrawnCard[] => {
    const engine = createDrawEngine();
    return engine.drawMultipleCards(count, { allowReversed, shuffleCount: 3 });
  },

  /**
   * 只抽大阿卡纳
   */
  majorOnly: (count: number = 1, allowReversed: boolean = true): DrawnCard[] => {
    const engine = createDrawEngine();
    const deckConfig: DeckConfig = {
      includeMajorArcana: true,
      includeMinorArcana: false
    };
    return engine.drawMultipleCards(count, { allowReversed, shuffleCount: 3 }, deckConfig);
  }
};
