"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ai_interpreter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai-interpreter */ \"(app-pages-browser)/./src/lib/ai-interpreter.ts\");\n/* harmony import */ var _constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants/tarot-cards */ \"(app-pages-browser)/./src/constants/tarot-cards.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 简单的文本格式化函数\nfunction formatText(text) {\n    // 按行分割文本\n    const lines = text.split('\\n');\n    return lines.map((line, lineIndex)=>{\n        // 处理标题\n        if (line.startsWith('## ')) {\n            const title = line.slice(3); // 移除 \"## \"\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold text-amber-300 mb-4 mt-6 flex items-center\",\n                children: formatInlineText(title)\n            }, lineIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this);\n        }\n        if (line.startsWith('### ')) {\n            const title = line.slice(4); // 移除 \"### \"\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-amber-200 mb-3 mt-4\",\n                children: formatInlineText(title)\n            }, lineIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this);\n        }\n        // 处理普通段落\n        if (line.trim() === '') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, lineIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-purple-100 leading-relaxed mb-4\",\n            children: formatInlineText(line)\n        }, lineIndex, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    });\n}\n// 处理行内格式（粗体等）\nfunction formatInlineText(text) {\n    const parts = text.split(/(\\*\\*[^*]+\\*\\*)/g);\n    return parts.map((part, index)=>{\n        if (part.startsWith('**') && part.endsWith('**')) {\n            const content = part.slice(2, -2);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"text-amber-200 font-bold\",\n                children: content\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this);\n        }\n        return part;\n    });\n}\nfunction Home() {\n    var _this = this;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [question, setQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shuffling, setShuffling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deckCards, setDeckCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCards, setSelectedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [revealedCards, setRevealedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maxCards, setMaxCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentReading, setCurrentReading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [interpretingProgress, setInterpretingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [cardLayout, setCardLayout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('circle');\n    const [allCardsFlipped, setAllCardsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [flippedCards, setFlippedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedCardback, setSelectedCardback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('classic');\n    // 加载用户选择的卡背样式\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (true) {\n                const savedCardback = localStorage.getItem('selectedCardback');\n                if (savedCardback) {\n                    setSelectedCardback(savedCardback);\n                }\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    const startReading = (type)=>{\n        setSelectedType(type);\n        setCurrentStep('question');\n        // 设置牌数\n        const cardCounts = {\n            'single': 1,\n            'three': 3,\n            'love': 3,\n            'career': 3,\n            'cross': 5,\n            'horseshoe': 7,\n            'celtic': 10,\n            'star': 7,\n            'pyramid': 6,\n            'moon': 4,\n            'chakra': 7,\n            'decision': 5\n        };\n        setMaxCards(cardCounts[type]);\n    };\n    const startPreview = ()=>{\n        setCurrentStep('preview');\n        setAllCardsFlipped(false);\n        setFlippedCards(new Set());\n    };\n    const startCardbackSelection = ()=>{\n        setCurrentStep('cardback');\n    };\n    const selectCardback = (style)=>{\n        setSelectedCardback(style);\n        // 保存到localStorage\n        if (true) {\n            localStorage.setItem('selectedCardback', style);\n        }\n    };\n    // 渲染不同风格的卡背\n    const renderCardback = function(style) {\n        let isPreview = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, isSmall = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const baseClasses = isPreview ? \"w-32 h-48 mx-auto\" : \"w-full h-full\";\n        const symbolSize = isSmall ? isPreview ? \"text-lg\" : \"text-xs\" : isPreview ? \"text-xl\" : \"text-lg\";\n        const decorSize = isSmall ? \"text-xs\" : isPreview ? \"text-sm\" : \"text-xs\";\n        switch(style){\n            case 'classic':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border-2 border-amber-400/40 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-amber-300 \".concat(symbolSize),\n                                children: \"⚜\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 right-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, _this);\n            case 'luxury':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-black overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-amber-400/80 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-amber-400/60 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-12 h-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border-2 border-amber-400/70 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-2 border border-amber-400/60 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-amber-400 \".concat(symbolSize),\n                                            children: \"\\uD83C\\uDF1F\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-1/2 transform -translate-x-1/2 text-amber-400/80 text-xs tracking-widest\",\n                            children: \"TAROT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-amber-400/80 text-xs tracking-widest\",\n                            children: \"MYSTICAL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 11\n                }, _this);\n            case 'sacred':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-slate-900 via-purple-950 to-indigo-950 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(251,191,36,0.15)_0%,transparent_60%)]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-amber-400/50 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-10 h-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 text-amber-400/80 \".concat(symbolSize, \" flex items-center justify-center\"),\n                                        children: \"\\uD83D\\uDD4E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-2 border border-amber-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 left-3 text-amber-400/60 text-xs\",\n                            children: \"✡\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 right-3 text-amber-400/60 text-xs\",\n                            children: \"☪\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 left-3 text-amber-400/60 text-xs\",\n                            children: \"☯\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 right-3 text-amber-400/60 text-xs\",\n                            children: \"\\uD83D\\uDD2F\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, _this);\n            case 'cosmic':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-indigo-950 via-blue-950 to-purple-950 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.1)_0%,transparent_70%)]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-blue-400/50 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-300 \".concat(symbolSize, \" animate-pulse\"),\n                                    children: \"\\uD83C\\uDF0C\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 text-blue-400/60 text-xs\",\n                            children: \"✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 text-blue-400/60 text-xs\",\n                            children: \"⭐\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-2 text-blue-400/60 text-xs\",\n                            children: \"\\uD83C\\uDF19\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 right-2 text-blue-400/60 text-xs\",\n                            children: \"☄️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-blue-300/80 text-xs tracking-wider\",\n                            children: \"COSMIC\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, _this);\n            case 'elegant':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border border-gray-300/40 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-8 h-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border border-gray-300/60 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-1 bg-gradient-to-br from-gray-300/20 to-gray-400/20 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 \".concat(symbolSize),\n                                            children: \"◇\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 left-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 right-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 left-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 right-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-gray-300/70 text-xs tracking-widest\",\n                            children: \"ELEGANT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, _this);\n            case 'royal':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-purple-900 via-indigo-900 to-purple-900 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-yellow-400/60 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-yellow-400/40 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 \".concat(symbolSize),\n                                    children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 text-yellow-400/60 text-xs\",\n                            children: \"♠\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 text-yellow-400/60 text-xs\",\n                            children: \"♥\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-2 text-yellow-400/60 text-xs\",\n                            children: \"♣\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 right-2 text-yellow-400/60 text-xs\",\n                            children: \"♦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-yellow-400/80 text-xs tracking-widest\",\n                            children: \"ROYAL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, _this);\n            case 'minimal':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-slate-800 to-slate-900 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-2 border border-white/30 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/80 \".concat(symbolSize),\n                                children: \"◯\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-white/60 text-xs tracking-widest\",\n                            children: \"MINIMAL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, _this);\n            case 'ai-generated':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-radial from-amber-400/10 via-transparent to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(251,191,36,0.1)_0%,transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(251,191,36,0.1)_0%,transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-contain bg-center bg-no-repeat\",\n                            style: {\n                                backgroundImage: 'url(/images/cardback-ai.png)'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-amber-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 11\n                }, _this);\n            default:\n                return renderCardback('classic', isPreview);\n        }\n    };\n    const toggleAllCards = ()=>{\n        const newFlipped = !allCardsFlipped;\n        setAllCardsFlipped(newFlipped);\n        if (newFlipped) {\n            // 全部翻到正面\n            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n            setFlippedCards(new Set(allCards.map((card)=>card.id.toString())));\n        } else {\n            // 全部翻到反面\n            setFlippedCards(new Set());\n        }\n    };\n    const toggleSingleCard = (cardId)=>{\n        const newFlippedCards = new Set(flippedCards);\n        if (newFlippedCards.has(cardId)) {\n            newFlippedCards.delete(cardId);\n        } else {\n            newFlippedCards.add(cardId);\n        }\n        setFlippedCards(newFlippedCards);\n        // 检查是否所有卡牌都翻开了\n        const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n        const allFlipped = newFlippedCards.size === allCards.length;\n        setAllCardsFlipped(allFlipped);\n    };\n    const startShuffle = ()=>{\n        setCurrentStep('shuffle');\n        setShuffling(true);\n        // 生成牌组 - 使用实际的78张牌\n        const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n        const cards = Array.from({\n            length: allCards.length\n        }, (_, i)=>i);\n        setDeckCards(cards);\n        // 模拟洗牌过程\n        setTimeout(()=>{\n            setShuffling(false);\n            setCurrentStep('draw');\n        }, 3000);\n    };\n    const selectCard = (cardIndex)=>{\n        if (selectedCards.includes(cardIndex) || selectedCards.length >= maxCards) return;\n        const newSelectedCards = [\n            ...selectedCards,\n            cardIndex\n        ];\n        setSelectedCards(newSelectedCards);\n        // 如果选够了牌，立即跳转到解读界面并开始AI解读\n        if (newSelectedCards.length === maxCards) {\n            setTimeout(()=>{\n                setCurrentStep('interpreting');\n                performReading(newSelectedCards);\n            }, 800);\n        }\n    };\n    const performReading = async (cardIndices)=>{\n        try {\n            // 重置进度\n            setInterpretingProgress(0);\n            // 模拟进度更新\n            const progressInterval = setInterval(()=>{\n                setInterpretingProgress((prev)=>{\n                    if (prev >= 90) {\n                        clearInterval(progressInterval);\n                        return 90; // 保持在90%，等AI完成后再到100%\n                    }\n                    // 确保进度不会超过90%\n                    const increment = Math.random() * 8 + 2; // 2-10的随机增量\n                    return Math.min(prev + increment, 90);\n                });\n            }, 500);\n            // 根据选中的牌索引生成塔罗牌数据\n            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n            // 加载已上传的图片\n            const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n            const selectedTarotCards = cardIndices.map((index, position)=>{\n                // 从完整的78张牌中选择\n                const cardData = allCards[index % allCards.length];\n                const orientation = Math.random() > 0.5 ? '正位' : '逆位';\n                return {\n                    name: cardData.name,\n                    imageUrl: uploadedImages[cardData.id] || cardData.imageUrl,\n                    orientation,\n                    position: getPositionName(position)\n                };\n            });\n            // 设置显示的牌\n            setRevealedCards(selectedTarotCards);\n            // 准备AI解读请求\n            const spreadTypes = {\n                'single': '单张牌占卜',\n                'three': '三张牌占卜',\n                'love': '爱情三张牌占卜',\n                'career': '事业三张牌占卜',\n                'cross': '十字牌阵',\n                'celtic': '凯尔特十字牌阵',\n                'horseshoe': '马蹄铁牌阵',\n                'star': '七芒星牌阵',\n                'pyramid': '金字塔牌阵',\n                'moon': '月相牌阵',\n                'chakra': '脉轮牌阵',\n                'decision': '决策牌阵'\n            };\n            const readingTypes = {\n                'single': '快速指引',\n                'three': '过去现在未来',\n                'love': '爱情运势',\n                'career': '事业发展',\n                'cross': '综合运势',\n                'celtic': '深度解读',\n                'horseshoe': '七个层面解读',\n                'star': '能量流动解读',\n                'pyramid': '层次递进解读',\n                'moon': '情感周期解读',\n                'chakra': '能量中心解读',\n                'decision': '选择路径解读'\n            };\n            const aiRequest = {\n                question,\n                cards: selectedTarotCards,\n                spreadType: spreadTypes[selectedType] || '塔罗占卜',\n                readingType: readingTypes[selectedType] || '综合解读'\n            };\n            // 调用AI解读\n            const aiResult = await (0,_lib_ai_interpreter__WEBPACK_IMPORTED_MODULE_2__.getAIInterpretation)(aiRequest);\n            // 完成进度并跳转\n            setInterpretingProgress(100);\n            setTimeout(()=>{\n                setCurrentReading({\n                    interpretation: aiResult.interpretation,\n                    error: aiResult.success ? null : aiResult.error || '解读生成失败'\n                });\n                setCurrentStep('result');\n            }, 500);\n        } catch (error) {\n            console.error('占卜过程出错:', error);\n            setInterpretingProgress(100);\n            setTimeout(()=>{\n                setCurrentReading({\n                    interpretation: '',\n                    error: '占卜过程中出现了问题，请重试。'\n                });\n                setCurrentStep('result');\n            }, 500);\n        }\n    };\n    const resetReading = ()=>{\n        setCurrentStep('select');\n        setSelectedType(null);\n        setQuestion('');\n        setDeckCards([]);\n        setSelectedCards([]);\n        setRevealedCards([]);\n        setCurrentReading(null);\n        setInterpretingProgress(0);\n        setAllCardsFlipped(false);\n        setFlippedCards(new Set());\n    };\n    // 渲染单个卡牌\n    const renderCard = (i, customStyle)=>{\n        const isGrid = cardLayout === 'grid';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>selectCard(i),\n            onMouseEnter: ()=>setHoveredCard(i),\n            onMouseLeave: ()=>setHoveredCard(null),\n            className: \"\".concat(isGrid ? 'relative' : 'absolute', \" cursor-pointer transform transition-all duration-300 \").concat(selectedCards.includes(i) ? 'scale-125 z-20' : hoveredCard === i ? 'scale-110 z-10' : 'hover:scale-105', \" \").concat(selectedCards.length >= maxCards && !selectedCards.includes(i) ? 'opacity-30 cursor-not-allowed' : '', \" \").concat(isGrid && selectedCards.includes(i) ? 'drop-shadow-[0_12px_25px_rgba(251,191,36,0.8)]' : isGrid && hoveredCard === i ? 'drop-shadow-[0_6px_15px_rgba(251,191,36,0.4)]' : isGrid ? 'drop-shadow-[0_3px_8px_rgba(0,0,0,0.2)]' : ''),\n            style: customStyle,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(isGrid ? 'w-[63px] h-[96px]' : 'w-[49px] h-[73px]', \" rounded-[6px] border transition-all duration-300 overflow-hidden \").concat(selectedCards.includes(i) ? 'border-amber-400 shadow-lg shadow-amber-400/30' : 'border-amber-400'),\n                    children: selectedCards.includes(i) ? // 已选中状态 - 发光效果\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full bg-gradient-to-br from-amber-400/40 to-amber-600/40 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-amber-300 \".concat(isGrid ? 'text-lg' : 'text-sm'),\n                            children: \"✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 13\n                    }, this) : // 未选中状态 - 使用用户选择的卡背样式\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: renderCardback(selectedCardback, false, !isGrid)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, this),\n                selectedCards.includes(i) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute \".concat(isGrid ? '-top-2 -right-2 w-6 h-6' : '-top-1 -right-1 w-5 h-5', \" bg-amber-400 rounded-full flex items-center justify-center text-xs font-bold text-purple-900\"),\n                    children: selectedCards.indexOf(i) + 1\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, i, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 482,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染不同的卡牌布局\n    const renderCardLayout = ()=>{\n        const totalCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)().length;\n        if (cardLayout === 'circle') {\n            // 圆形布局\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-4xl mx-auto h-96 mb-8 animate-in fade-in duration-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-80 h-80\",\n                        children: [\n                            Array.from({\n                                length: totalCards\n                            }, (_, i)=>{\n                                const angleStep = 360 / totalCards;\n                                const angle = i * angleStep;\n                                const radius = 130;\n                                const radian = angle * Math.PI / 180;\n                                const x = Math.cos(radian) * radius;\n                                const y = Math.sin(radian) * radius;\n                                return renderCard(i, {\n                                    left: \"calc(50% + \".concat(x, \"px)\"),\n                                    top: \"calc(50% + \".concat(y, \"px)\"),\n                                    transform: \"translate(-50%, -50%) rotate(\".concat(angle + 90, \"deg) \").concat(selectedCards.includes(i) ? 'scale(1.25)' : hoveredCard === i ? 'scale(1.1)' : ''),\n                                    zIndex: selectedCards.includes(i) ? 20 : hoveredCard === i ? 10 : 1,\n                                    filter: selectedCards.includes(i) ? 'drop-shadow(0 12px 25px rgba(251, 191, 36, 0.8))' : hoveredCard === i ? 'drop-shadow(0 6px 15px rgba(251, 191, 36, 0.4))' : 'drop-shadow(0 3px 8px rgba(0, 0, 0, 0.2))',\n                                    transition: 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)'\n                                });\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-gradient-to-br from-purple-900 to-violet-900 rounded-full border-2 border-amber-400/50 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl animate-pulse\",\n                                    children: \"\\uD83D\\uDD2E\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 542,\n                columnNumber: 9\n            }, this);\n        } else if (cardLayout === 'spiral') {\n            // 🌀 螺旋布局 - 黄金螺旋展开\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-6xl mx-auto h-[600px] mb-8 animate-in fade-in duration-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full\",\n                        children: [\n                            Array.from({\n                                length: totalCards\n                            }, (_, i)=>{\n                                const turns = 3; // 螺旋圈数\n                                const maxRadius = 250;\n                                const angle = i / totalCards * turns * 2 * Math.PI;\n                                const radius = i / totalCards * maxRadius;\n                                const x = Math.cos(angle) * radius;\n                                const y = Math.sin(angle) * radius;\n                                return renderCard(i, {\n                                    left: \"calc(50% + \".concat(x, \"px)\"),\n                                    top: \"calc(50% + \".concat(y, \"px)\"),\n                                    transform: \"translate(-50%, -50%) rotate(\".concat(angle * 180 / Math.PI + 90, \"deg) \").concat(selectedCards.includes(i) ? 'scale(1.3) translateY(-15px)' : hoveredCard === i ? 'scale(1.15) translateY(-8px)' : ''),\n                                    zIndex: selectedCards.includes(i) ? 200 : hoveredCard === i ? 150 : totalCards - i,\n                                    filter: selectedCards.includes(i) ? 'drop-shadow(0 15px 30px rgba(251, 191, 36, 0.8))' : hoveredCard === i ? 'drop-shadow(0 8px 20px rgba(251, 191, 36, 0.4))' : 'drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3))',\n                                    transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'\n                                });\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-20 h-20 bg-gradient-to-br from-amber-400/20 to-purple-900 rounded-full border-2 border-amber-400/60 flex items-center justify-center shadow-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-1 bg-gradient-to-br from-purple-800/50 to-violet-900/50 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative text-3xl animate-spin\",\n                                            style: {\n                                                animationDuration: '8s'\n                                            },\n                                            children: \"\\uD83C\\uDF00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 577,\n                columnNumber: 9\n            }, this);\n        } else if (cardLayout === 'arc') {\n            // 🌙 多层弧形布局\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-6xl mx-auto h-[300px] mb-8 animate-in fade-in duration-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full\",\n                        children: [\n                            Array.from({\n                                length: totalCards\n                            }, (_, i)=>{\n                                const arcs = 4; // 4层弧形\n                                const cardsPerArc = Math.ceil(totalCards / arcs);\n                                const currentArc = Math.floor(i / cardsPerArc);\n                                const posInArc = i % cardsPerArc;\n                                const totalInArc = Math.min(cardsPerArc, totalCards - currentArc * cardsPerArc);\n                                const baseRadius = 80 + currentArc * 50;\n                                const arcAngle = 180; // 每层弧形180度\n                                const angleStep = arcAngle / Math.max(1, totalInArc - 1);\n                                const startAngle = -arcAngle / 2;\n                                const angle = startAngle + posInArc * angleStep;\n                                const radian = angle * Math.PI / 180;\n                                const x = Math.cos(radian) * baseRadius;\n                                const y = Math.sin(radian) * baseRadius * 0.6; // 压扁\n                                return renderCard(i, {\n                                    left: \"calc(50% + \".concat(x, \"px)\"),\n                                    top: \"calc(50% + \".concat(y + 60, \"px)\"),\n                                    transform: \"translate(-50%, -50%) rotate(\".concat(angle * 0.5, \"deg) \").concat(selectedCards.includes(i) ? 'scale(1.25) translateY(-12px)' : hoveredCard === i ? 'scale(1.1) translateY(-6px)' : ''),\n                                    zIndex: selectedCards.includes(i) ? 200 : hoveredCard === i ? 150 : arcs - currentArc,\n                                    filter: selectedCards.includes(i) ? 'drop-shadow(0 12px 25px rgba(251, 191, 36, 0.8))' : hoveredCard === i ? 'drop-shadow(0 6px 15px rgba(251, 191, 36, 0.4))' : 'drop-shadow(0 3px 8px rgba(0, 0, 0, 0.2))',\n                                    transition: 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)'\n                                });\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 translate-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-16 h-16 bg-gradient-to-br from-indigo-400/20 to-purple-900 rounded-full border border-indigo-400/60 flex items-center justify-center shadow-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative text-2xl animate-pulse\",\n                                        children: \"\\uD83C\\uDF19\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 615,\n                columnNumber: 9\n            }, this);\n        } else {\n            // 默认：重叠排列布局 (grid)\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full mb-8 animate-in fade-in duration-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex overflow-x-auto pb-4 pt-8 px-8\",\n                        style: {\n                            scrollbarWidth: 'thin',\n                            scrollbarColor: '#d97706 transparent'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: Array.from({\n                                length: totalCards\n                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    style: {\n                                        marginLeft: i === 0 ? '0' : '-50px',\n                                        zIndex: selectedCards.includes(i) ? 100 : totalCards - i\n                                    },\n                                    children: renderCard(i)\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-purple-300 bg-purple-800/20 px-4 py-2 rounded-full border border-amber-400/20\",\n                            children: \"← 滑动查看所有牌卡 →\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 661,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    const getSpreadType = ()=>{\n        if (selectedType === 'single') return 'single';\n        if (selectedType === 'three' || selectedType === 'love' || selectedType === 'career') return 'three';\n        if (selectedType === 'cross') return 'cross';\n        if (selectedType === 'celtic') return 'celtic';\n        return 'single';\n    };\n    const getPositionName = (index)=>{\n        const spreadType = getSpreadType();\n        if (spreadType === 'single') {\n            return '当前指引';\n        } else if (spreadType === 'three') {\n            return [\n                '过去',\n                '现在',\n                '未来'\n            ][index] || \"第\".concat(index + 1, \"张牌\");\n        }\n        return \"第\".concat(index + 1, \"张牌\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950 text-white overflow-hidden relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] left-[15%] w-1 h-1 bg-amber-300 rounded-full animate-pulse shadow-lg shadow-amber-300/50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[25%] right-[20%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-1000 shadow-sm shadow-amber-200/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[8%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-500 shadow-lg shadow-amber-400/60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[60%] right-[12%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[75%] left-[25%] w-1 h-1 bg-amber-200 rounded-full animate-pulse delay-2000 shadow-md shadow-amber-200/40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[35%] right-[35%] w-0.5 h-0.5 bg-amber-400 rounded-full animate-pulse delay-700\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[80%] right-[40%] w-1 h-1 bg-amber-300 rounded-full animate-pulse delay-1200 shadow-lg shadow-amber-300/50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[15%] left-[60%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-800\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[50%] left-[70%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-300 shadow-lg shadow-amber-400/60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[90%] left-[45%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1800\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[20%] left-[30%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-60 animate-pulse delay-2500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[70%] right-[60%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-40 animate-pulse delay-3000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[45%] left-[85%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-50 animate-pulse delay-2200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 733,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 right-1/4 w-80 h-80 bg-amber-400/5 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 734,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    currentStep !== 'select' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: resetReading,\n                                className: \"group flex items-center space-x-3 bg-gradient-to-r from-purple-800/30 to-violet-800/30 hover:from-purple-700/40 hover:to-violet-700/40 backdrop-blur-md rounded-2xl px-8 py-4 transition-all duration-300 border border-amber-400/20 hover:border-amber-400/40 shadow-lg hover:shadow-amber-400/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-amber-300 group-hover:text-amber-200 transition-colors text-lg\",\n                                        children: \"←\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-amber-100 group-hover:text-white transition-colors font-medium\",\n                                        children: \"重新开始\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full transition-all duration-500 \".concat(i <= [\n                                                    'select',\n                                                    'question',\n                                                    'shuffle',\n                                                    'draw',\n                                                    'result',\n                                                    'preview',\n                                                    'cardback'\n                                                ].indexOf(currentStep) ? 'bg-gradient-to-r from-amber-400 to-amber-300 shadow-lg shadow-amber-400/50' : 'bg-purple-300/20 border border-purple-300/30')\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-amber-200 font-medium bg-purple-800/20 px-4 py-2 rounded-full border border-amber-400/20\",\n                                        children: {\n                                            'select': '✨ 选择类型',\n                                            'question': '💭 输入问题',\n                                            'spread': '🔮 准备占卜',\n                                            'shuffle': '🌀 洗牌',\n                                            'draw': '🎴 选牌',\n                                            'result': '📜 解读结果',\n                                            'interpreting': '🤖 AI解读中',\n                                            'preview': '🎴 预览塔罗牌',\n                                            'cardback': '🎨 卡背设计'\n                                        }[currentStep]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-32\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 13\n                            }, this),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'select' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative inline-block mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-amber-400/20 to-purple-400/20 blur-3xl rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative text-8xl mb-6 animate-pulse\",\n                                                children: \"\\uD83D\\uDD2E\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-2 -right-2 text-2xl animate-spin-slow\",\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-2 -left-2 text-xl animate-bounce\",\n                                                children: \"\\uD83C\\uDF19\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-6xl font-bold mb-8 bg-gradient-to-r from-amber-300 via-amber-200 to-amber-100 bg-clip-text text-transparent tracking-wider drop-shadow-lg\",\n                                        children: \"神秘塔罗\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-purple-200 mb-12 tracking-wide font-light\",\n                                        children: \"✨ 探索命运的奥秘 \\xb7 窥见未来的真相 ✨\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-5xl mx-auto bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-10 border border-amber-400/20 shadow-2xl shadow-purple-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold mb-8 text-amber-300 text-center\",\n                                                children: \"✨ 塔罗占卜指引 ✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-8 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83C\\uDFAF\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"专注你的问题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"在内心深处思考你想要了解的事情，让宇宙感受到你的诚意\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83C\\uDF00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"神秘洗牌仪式\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 802,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"观看古老的塔罗牌洗牌过程，感受神秘力量的流动\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 803,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83D\\uDC46\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"凭直觉选牌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"从78张牌中选择命运为你准备的牌，相信你的第一感觉\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83D\\uDCDC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 814,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"专业解读\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"获得深度的塔罗牌解读和人生指引，照亮前行的道路\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-center mb-16 text-amber-300\",\n                                        children: \"\\uD83D\\uDD2E 选择你的占卜类型 \\uD83D\\uDD2E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 826,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                        children: [\n                                            {\n                                                type: 'preview',\n                                                icon: '🎴',\n                                                title: '预览塔罗牌',\n                                                desc: '查看78张塔罗牌',\n                                                tag: '预览模式 · 实用',\n                                                color: 'from-emerald-400 to-emerald-600',\n                                                special: true\n                                            },\n                                            {\n                                                type: 'cardback',\n                                                icon: '🎨',\n                                                title: '卡背设计',\n                                                desc: '选择塔罗牌背面',\n                                                tag: '设计选择 · 个性',\n                                                color: 'from-rose-400 to-rose-600',\n                                                special: true\n                                            },\n                                            {\n                                                type: 'single',\n                                                icon: '🌟',\n                                                title: '单牌指引',\n                                                desc: '简单直接的答案',\n                                                tag: '1张牌 · 初级',\n                                                color: 'from-amber-400 to-amber-600'\n                                            },\n                                            {\n                                                type: 'three',\n                                                icon: '⏳',\n                                                title: '时间三牌',\n                                                desc: '过去-现在-未来',\n                                                tag: '3张牌 · 推荐',\n                                                color: 'from-purple-400 to-purple-600'\n                                            },\n                                            {\n                                                type: 'cross',\n                                                icon: '✚',\n                                                title: '十字占卜',\n                                                desc: '核心-影响-结果',\n                                                tag: '5张牌 · 中级',\n                                                color: 'from-violet-400 to-violet-600'\n                                            },\n                                            {\n                                                type: 'celtic',\n                                                icon: '🎯',\n                                                title: '凯尔特十字',\n                                                desc: '最详细的全面解读',\n                                                tag: '10张牌 · 高级',\n                                                color: 'from-indigo-400 to-indigo-600'\n                                            },\n                                            {\n                                                type: 'love',\n                                                icon: '💕',\n                                                title: '爱情解读',\n                                                desc: '专门的情感占卜',\n                                                tag: '3张牌 · 专题',\n                                                color: 'from-pink-400 to-pink-600'\n                                            },\n                                            {\n                                                type: 'career',\n                                                icon: '💼',\n                                                title: '事业指导',\n                                                desc: '职场发展方向',\n                                                tag: '3张牌 · 专题',\n                                                color: 'from-emerald-400 to-emerald-600'\n                                            },\n                                            {\n                                                type: 'horseshoe',\n                                                icon: '🐎',\n                                                title: '马蹄铁牌阵',\n                                                desc: '七个层面的深度分析',\n                                                tag: '7张牌 · 高级',\n                                                color: 'from-orange-400 to-orange-600'\n                                            },\n                                            {\n                                                type: 'star',\n                                                icon: '⭐',\n                                                title: '七芒星牌阵',\n                                                desc: '能量流动与平衡',\n                                                tag: '7张牌 · 神秘',\n                                                color: 'from-cyan-400 to-cyan-600'\n                                            },\n                                            {\n                                                type: 'pyramid',\n                                                icon: '🔺',\n                                                title: '金字塔牌阵',\n                                                desc: '层次递进的智慧',\n                                                tag: '6张牌 · 中级',\n                                                color: 'from-yellow-400 to-yellow-600'\n                                            },\n                                            {\n                                                type: 'moon',\n                                                icon: '🌙',\n                                                title: '月相牌阵',\n                                                desc: '情感周期与直觉',\n                                                tag: '4张牌 · 灵性',\n                                                color: 'from-blue-400 to-blue-600'\n                                            },\n                                            {\n                                                type: 'chakra',\n                                                icon: '🧘',\n                                                title: '脉轮牌阵',\n                                                desc: '七大能量中心',\n                                                tag: '7张牌 · 疗愈',\n                                                color: 'from-teal-400 to-teal-600'\n                                            },\n                                            {\n                                                type: 'decision',\n                                                icon: '⚖️',\n                                                title: '决策牌阵',\n                                                desc: '选择与后果分析',\n                                                tag: '5张牌 · 实用',\n                                                color: 'from-slate-400 to-slate-600'\n                                            }\n                                        ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"group cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2\",\n                                                onClick: ()=>{\n                                                    if (item.type === 'preview') {\n                                                        startPreview();\n                                                    } else if (item.type === 'cardback') {\n                                                        startCardbackSelection();\n                                                    } else {\n                                                        startReading(item.type);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gradient-to-br from-purple-900/60 to-violet-900/60 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 shadow-2xl overflow-hidden hover:shadow-amber-400/20 transition-all duration-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-br from-amber-400/5 to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 text-amber-300 opacity-60 group-hover:opacity-100 transition-opacity duration-300\",\n                                                            children: \"✨\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 text-amber-300 opacity-40 group-hover:opacity-80 transition-opacity duration-300\",\n                                                            children: \"\\uD83C\\uDF19\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative z-10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300\",\n                                                                    children: item.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 967,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold mb-3 text-center text-amber-100 group-hover:text-amber-50 transition-colors duration-300\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-purple-200 text-center mb-6 leading-relaxed text-sm\",\n                                                                    children: item.desc\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-block px-4 py-2 bg-gradient-to-r \".concat(item.color, \" rounded-full text-sm font-semibold text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300\"),\n                                                                        children: item.tag\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 977,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 966,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 rounded-3xl border border-amber-400/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 984,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 958,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, item.type, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 825,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'question' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83D\\uDCAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 996,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"请输入你的问题\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 997,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: \"让塔罗牌为你揭示答案\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: question,\n                                        onChange: (e)=>setQuestion(e.target.value),\n                                        placeholder: \"请输入你想要了解的问题...\",\n                                        className: \"w-full h-32 bg-purple-800/30 border border-amber-400/30 rounded-2xl p-4 text-white placeholder-purple-300 focus:outline-none focus:border-amber-400/60 transition-colors resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: startShuffle,\n                                            disabled: !question.trim(),\n                                            className: \"bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 disabled:from-gray-600 disabled:to-gray-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 disabled:scale-100 shadow-lg\",\n                                            children: \"开始占卜 ✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1009,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1001,\n                                columnNumber: 13\n                            }, this),\n                            (()=>{\n                                const spreadDetails = {\n                                    'single': {\n                                        icon: '🌟',\n                                        title: '单牌指引',\n                                        description: '最简单直接的占卜方式，适合快速获得指引和建议。一张牌能够提供清晰明确的答案，特别适合日常决策和即时问题。',\n                                        usage: '适用于：日常选择、当下困惑、快速指引、简单问题',\n                                        examples: '今天我应该做什么？这个决定对我有利吗？我现在的状态如何？',\n                                        tips: '问题要具体明确，避免过于复杂的多重问题'\n                                    },\n                                    'three': {\n                                        icon: '⏳',\n                                        title: '时间三牌',\n                                        description: '经典的过去-现在-未来牌阵，帮你了解事情的发展脉络。通过三个时间维度，全面分析问题的来龙去脉和发展趋势。',\n                                        usage: '适用于：了解发展趋势、分析问题根源、预测未来走向、制定计划',\n                                        examples: '我的感情会如何发展？这个项目的前景怎样？我的职业规划如何？',\n                                        tips: '适合需要了解事情发展过程的问题，可以是任何领域的问题'\n                                    },\n                                    'cross': {\n                                        icon: '✚',\n                                        title: '十字占卜',\n                                        description: '五张牌组成的十字形牌阵，深入分析问题的核心、影响因素和最终结果。提供全面而平衡的视角。',\n                                        usage: '适用于：复杂问题分析、多角度思考、寻找解决方案、重要决策',\n                                        examples: '我应该换工作吗？这段关系值得继续吗？如何解决当前的困境？',\n                                        tips: '适合需要深入分析的重要问题，会从多个角度给出建议'\n                                    },\n                                    'celtic': {\n                                        icon: '🎯',\n                                        title: '凯尔特十字',\n                                        description: '最经典和详细的塔罗牌阵，十张牌提供最全面的解读。涵盖问题的各个方面，包括潜意识、外在影响、内在力量等。',\n                                        usage: '适用于：人生重大问题、全面生活分析、深度自我探索、复杂情况',\n                                        examples: '我的人生方向是什么？如何实现我的目标？我该如何面对人生转折？',\n                                        tips: '适合最重要的人生问题，需要充足时间来理解和消化解读结果'\n                                    },\n                                    'love': {\n                                        icon: '💕',\n                                        title: '爱情解读',\n                                        description: '专门针对感情问题设计的三牌阵，分析你的感情状态、对方的想法和关系的发展。特别适合情感困惑。',\n                                        usage: '适用于：恋爱关系、婚姻问题、暗恋困扰、分手复合、感情选择',\n                                        examples: '他/她对我有感觉吗？我们的关系会有结果吗？我应该表白吗？',\n                                        tips: '专注于感情相关问题，可以询问具体的感情状况和发展'\n                                    },\n                                    'career': {\n                                        icon: '💼',\n                                        title: '事业指导',\n                                        description: '专门分析职业发展的三牌阵，从当前状况、发展机会和行动建议三个角度指导你的职业规划。',\n                                        usage: '适用于：职业选择、工作转换、升职加薪、创业决策、职场关系',\n                                        examples: '我应该跳槽吗？如何在职场上获得成功？这个投资项目可行吗？',\n                                        tips: '专注于事业和财务相关问题，可以询问具体的职业发展策略'\n                                    },\n                                    'horseshoe': {\n                                        icon: '🐎',\n                                        title: '马蹄铁牌阵',\n                                        description: '七张牌组成的马蹄形牌阵，提供七个层面的深度分析。从过去影响到未来结果，全面解读问题的各个维度。',\n                                        usage: '适用于：复杂生活问题、多方面分析、长期规划、人际关系、重大变化',\n                                        examples: '我该如何处理复杂的人际关系？如何平衡工作和生活？人生下一阶段的规划？',\n                                        tips: '适合需要多角度深入分析的复杂问题，会提供非常详细的指导'\n                                    },\n                                    'star': {\n                                        icon: '⭐',\n                                        title: '七芒星牌阵',\n                                        description: '神秘的七芒星形牌阵，探索精神层面的能量流动。帮助你了解内在力量、精神成长和能量平衡。',\n                                        usage: '适用于：精神成长、内在探索、能量平衡、灵性问题、创意启发',\n                                        examples: '我的精神状态如何？如何提升自己的能量？我的天赋是什么？',\n                                        tips: '适合探索内在世界和精神层面的问题，注重心灵成长和自我认知'\n                                    },\n                                    'pyramid': {\n                                        icon: '🔺',\n                                        title: '金字塔牌阵',\n                                        description: '六张牌组成的金字塔形牌阵，层次递进地揭示问题的深层含义。从基础到顶峰，逐步深入问题核心。',\n                                        usage: '适用于：目标实现、层次分析、逐步规划、技能提升、个人成长',\n                                        examples: '如何实现我的目标？我需要提升哪些能力？如何一步步改善现状？',\n                                        tips: '适合需要分步骤解决的问题，会提供循序渐进的建议和指导'\n                                    },\n                                    'moon': {\n                                        icon: '🌙',\n                                        title: '月相牌阵',\n                                        description: '四张牌对应月亮的四个相位，探索情感周期和直觉指引。特别适合了解情感变化和内在直觉。',\n                                        usage: '适用于：情感周期、直觉开发、内心声音、情绪管理、女性问题',\n                                        examples: '我的情绪为什么会波动？如何倾听内心的声音？我的直觉在告诉我什么？',\n                                        tips: '适合探索情感和直觉相关的问题，特别关注内在感受和情绪变化'\n                                    },\n                                    'chakra': {\n                                        icon: '🧘',\n                                        title: '脉轮牌阵',\n                                        description: '七张牌对应人体七个脉轮能量中心，用于能量疗愈和身心平衡。帮助识别能量阻塞和平衡方法。',\n                                        usage: '适用于：身心健康、能量疗愈、情绪平衡、精神净化、整体wellness',\n                                        examples: '我的能量状态如何？哪个方面需要调整？如何保持身心平衡？',\n                                        tips: '适合关注身心健康和能量平衡的问题，会从整体wellness角度给出建议'\n                                    },\n                                    'decision': {\n                                        icon: '⚖️',\n                                        title: '决策牌阵',\n                                        description: '五张牌专门用于重要决策分析，比较不同选择的利弊和后果。帮助你做出明智的选择。',\n                                        usage: '适用于：重要选择、利弊分析、风险评估、机会把握、人生转折',\n                                        examples: '我应该选择A还是B？这个决定的后果是什么？哪个选择对我更有利？',\n                                        tips: '适合面临重要选择时使用，需要明确说明你在考虑的具体选项'\n                                    }\n                                };\n                                const currentSpread = spreadDetails[selectedType];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-purple-900/60 to-violet-900/60 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl mr-4\",\n                                                        children: currentSpread.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-amber-300\",\n                                                                children: currentSpread.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1130,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-200 mt-2\",\n                                                                children: currentSpread.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1127,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-amber-200 mb-3\",\n                                                                children: \"\\uD83D\\uDCCB 适用场景\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1137,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-100 text-sm leading-relaxed\",\n                                                                children: currentSpread.usage\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1138,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-amber-200 mb-3\",\n                                                                children: \"\\uD83D\\uDCA1 提问建议\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1141,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-100 text-sm leading-relaxed\",\n                                                                children: currentSpread.tips\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1142,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1135,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-amber-200 mb-3\",\n                                                        children: \"\\uD83C\\uDF1F 问题示例\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-100 text-sm leading-relaxed italic\",\n                                                        children: currentSpread.examples\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1124,\n                                    columnNumber: 17\n                                }, this);\n                            })()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 994,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'shuffle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-6\",\n                                children: \"\\uD83D\\uDD2E\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                children: \"正在洗牌...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-200 mb-8\",\n                                children: \"请静心等待，让宇宙的能量流动\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-6 gap-4 mb-8\",\n                                        children: Array.from({\n                                            length: 12\n                                        }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-[63px] h-[96px] bg-gradient-to-br from-purple-800 to-purple-900 rounded-[6px] border border-amber-400 transform transition-all duration-500 \".concat(shuffling ? 'animate-pulse scale-110 rotate-12' : ''),\n                                                style: {\n                                                    animationDelay: \"\".concat(i * 100, \"ms\")\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full overflow-hidden rounded-lg\",\n                                                    children: renderCardback(selectedCardback, false)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1176,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1166,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1164,\n                                        columnNumber: 15\n                                    }, this),\n                                    shuffling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl animate-spin\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1185,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1163,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1158,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'draw' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83C\\uDFB4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"选择你的牌\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-4\",\n                                        children: [\n                                            \"请凭直觉选择 \",\n                                            maxCards,\n                                            \" 张牌\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-300\",\n                                        children: [\n                                            \"已选择: \",\n                                            selectedCards.length,\n                                            \"/\",\n                                            maxCards\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center gap-4 mt-6 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCardLayout('circle'),\n                                                className: \"px-4 py-2 rounded-lg transition-all duration-300 \".concat(cardLayout === 'circle' ? 'bg-amber-400 text-purple-900 font-bold' : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'),\n                                                children: \"\\uD83D\\uDD2E 圆形\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCardLayout('grid'),\n                                                className: \"px-4 py-2 rounded-lg transition-all duration-300 \".concat(cardLayout === 'grid' ? 'bg-amber-400 text-purple-900 font-bold' : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'),\n                                                children: \"\\uD83D\\uDCF1 网格\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCardLayout('spiral'),\n                                                className: \"px-4 py-2 rounded-lg transition-all duration-300 \".concat(cardLayout === 'spiral' ? 'bg-amber-400 text-purple-900 font-bold' : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'),\n                                                children: \"\\uD83C\\uDF00 螺旋\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCardLayout('arc'),\n                                                className: \"px-4 py-2 rounded-lg transition-all duration-300 \".concat(cardLayout === 'arc' ? 'bg-amber-400 text-purple-900 font-bold' : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'),\n                                                children: \"\\uD83C\\uDF19 弧形\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1194,\n                                columnNumber: 13\n                            }, this),\n                            renderCardLayout(),\n                            selectedCards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-amber-300 mb-2\",\n                                                children: \"已选择的卡牌\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1252,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-200 text-sm\",\n                                                children: \"点击卡牌可以取消选择\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center gap-4 max-w-4xl mx-auto\",\n                                        children: selectedCards.map((cardIndex, index)=>{\n                                            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n                                            const card = allCards[cardIndex];\n                                            const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n                                            const imageUrl = uploadedImages[card.id] || card.imageUrl;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center cursor-pointer transform transition-all duration-300 hover:scale-105\",\n                                                onClick: ()=>{\n                                                    setSelectedCards(selectedCards.filter((i)=>i !== cardIndex));\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-3 border border-amber-400/30 min-w-[100px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-bold text-amber-300 mb-2\",\n                                                            children: [\n                                                                \"第\",\n                                                                index + 1,\n                                                                \"张\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1274,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-24 mx-auto mb-2 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                            children: imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: imageUrl,\n                                                                alt: card.name,\n                                                                className: \"w-full h-full object-cover\",\n                                                                onError: (e)=>{\n                                                                    e.currentTarget.style.display = 'none';\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1281,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg\",\n                                                                children: \"\\uD83C\\uDFB4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1290,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1279,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-semibold text-white truncate\",\n                                                            children: card.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1295,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1272,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, cardIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1265,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1255,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1250,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentStep('question'),\n                                    className: \"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors mr-4\",\n                                    children: \"重新选择问题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1307,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1193,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'interpreting' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-6\",\n                                children: \"\\uD83D\\uDD2E\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                children: \"神秘力量正在解读...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1320,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-200 mb-8\",\n                                children: \"请稍候，神秘的智慧正在为你揭示答案\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1321,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-6 mb-8 max-w-6xl mx-auto \".concat(revealedCards.length === 1 ? 'justify-center' : revealedCards.length <= 3 ? 'justify-center' : 'justify-center'),\n                                children: revealedCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-bold text-amber-300 mb-3\",\n                                                    children: card.position\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1332,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                    children: card.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: card.imageUrl,\n                                                        alt: card.name,\n                                                        className: \"w-full h-full object-cover transition-transform duration-300 scale-110 \".concat(card.orientation === '逆位' ? 'rotate-180' : ''),\n                                                        onError: ()=>{\n                                                        // 图片加载失败时会自动显示备用内容\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83C\\uDFB4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1337,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-semibold text-white mb-1\",\n                                                    children: card.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1355,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-purple-200\",\n                                                    children: card.orientation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1356,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1331,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1330,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-900/40 rounded-full h-2 mb-3 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-amber-400 to-amber-600 h-full rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(interpretingProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1365,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-purple-300 mb-2\",\n                                        children: [\n                                            \"解读进度: \",\n                                            Math.round(interpretingProgress),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-purple-200 text-sm\",\n                                        children: [\n                                            interpretingProgress < 30 && \"🌟 正在连接宇宙能量...\",\n                                            interpretingProgress >= 30 && interpretingProgress < 60 && \"✨ 分析牌面含义...\",\n                                            interpretingProgress >= 60 && interpretingProgress < 90 && \"🔮 生成专属解读...\",\n                                            interpretingProgress >= 90 && \"💫 即将完成...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1375,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1363,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.4s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1384,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1318,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'cardback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83C\\uDFA8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"卡背设计选择\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1396,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: \"选择您喜欢的塔罗牌背面设计风格\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1394,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\",\n                                children: [\n                                    {\n                                        id: 'classic',\n                                        name: '经典神秘',\n                                        desc: '传统紫色+金色，神秘符号',\n                                        preview: '⚜'\n                                    },\n                                    {\n                                        id: 'luxury',\n                                        name: '奢华金箔',\n                                        desc: '黑金配色，双重边框',\n                                        preview: '🌟'\n                                    },\n                                    {\n                                        id: 'sacred',\n                                        name: '神圣几何',\n                                        desc: '宗教符号，神圣图案',\n                                        preview: '🕎'\n                                    },\n                                    {\n                                        id: 'cosmic',\n                                        name: '宇宙星空',\n                                        desc: '蓝色星空，天体元素',\n                                        preview: '🌌'\n                                    },\n                                    {\n                                        id: 'elegant',\n                                        name: '优雅简约',\n                                        desc: '灰色系，简洁几何',\n                                        preview: '◇'\n                                    },\n                                    {\n                                        id: 'royal',\n                                        name: '皇室华丽',\n                                        desc: '紫金配色，皇室元素',\n                                        preview: '👑'\n                                    },\n                                    {\n                                        id: 'minimal',\n                                        name: '极简现代',\n                                        desc: '黑白简约，现代美学',\n                                        preview: '◯'\n                                    },\n                                    {\n                                        id: 'ai-generated',\n                                        name: 'AI专业版',\n                                        desc: '您的AI生成专业卡背',\n                                        preview: '🤖'\n                                    }\n                                ].map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2 \".concat(selectedCardback === style.id ? 'ring-2 ring-amber-400 ring-offset-2 ring-offset-purple-900' : ''),\n                                        onClick: ()=>selectCardback(style.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-6 border border-amber-400/20 shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-amber-300 mb-2 text-center\",\n                                                    children: style.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1460,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-200 text-sm text-center mb-6\",\n                                                    children: style.desc\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1461,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center mb-6\",\n                                                    children: renderCardback(style.id, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1464,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: selectedCardback === style.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-amber-400 to-amber-600 px-4 py-2 rounded-full text-white font-bold text-sm\",\n                                                        children: \"✅ 已选择\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1471,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-800/50 hover:bg-purple-700/50 px-4 py-2 rounded-full text-purple-200 font-medium text-sm transition-colors\",\n                                                        children: \"点击选择\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1475,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1469,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1459,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, style.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1452,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1401,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-amber-300 mb-6 text-center\",\n                                        children: \"当前选择预览\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1487,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center gap-8 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-purple-200 mb-4\",\n                                                        children: \"反面（卡背）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1491,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: renderCardback(selectedCardback, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1492,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl text-amber-300\",\n                                                children: \"↔️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-purple-200 mb-4\",\n                                                        children: \"正面（示例）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1500,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-32 h-48 mx-auto bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl mb-2\",\n                                                                    children: \"\\uD83C\\uDFB4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1503,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-amber-200\",\n                                                                    children: \"愚人牌\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1504,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1502,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1499,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-purple-300 text-sm\",\n                                            children: \"选择的卡背设计将应用到所有塔罗牌的反面显示\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1511,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1510,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1486,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // 保存选择并返回首页\n                                            if (true) {\n                                                localStorage.setItem('selectedCardback', selectedCardback);\n                                            }\n                                            resetReading();\n                                        },\n                                        className: \"bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg mr-4\",\n                                        children: \"✅ 应用选择\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1517,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>startPreview(),\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                        children: \"\\uD83C\\uDFB4 预览效果\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1530,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1516,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1393,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83C\\uDFB4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1543,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"塔罗牌预览\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1544,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: \"查看和测试您的78张塔罗牌图片效果\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1545,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center gap-4 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setAllCardsFlipped(false);\n                                                    setFlippedCards(new Set());\n                                                },\n                                                className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                                children: \"\\uD83D\\uDD04 全部反面\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1549,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setAllCardsFlipped(true);\n                                                    const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n                                                    setFlippedCards(new Set(allCards.map((card)=>card.id.toString())));\n                                                },\n                                                className: \"bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                                children: \"✨ 全部正面\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1558,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleAllCards,\n                                                className: \"bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                                children: \"\\uD83D\\uDD00 一键翻转\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1568,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1548,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1542,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center gap-6\",\n                                        children: (()=>{\n                                            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n                                            const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n                                            return allCards.map((card)=>{\n                                                const isFlipped = allCardsFlipped || flippedCards.has(card.id.toString());\n                                                const imageUrl = uploadedImages[card.id] || card.imageUrl;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px] cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2 group\",\n                                                        onClick: ()=>toggleSingleCard(card.id.toString()),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-bold text-amber-300 mb-3\",\n                                                                children: card.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1597,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center relative\",\n                                                                children: [\n                                                                    isFlipped ? // 正面 - 显示卡牌图片\n                                                                    imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: imageUrl,\n                                                                        alt: card.name,\n                                                                        className: \"w-full h-full object-cover transition-transform duration-300 scale-110\",\n                                                                        onError: (e)=>{\n                                                                            // 图片加载失败时显示备用内容\n                                                                            e.currentTarget.style.display = 'none';\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1606,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl\",\n                                                                        children: \"\\uD83C\\uDFB4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1616,\n                                                                        columnNumber: 33\n                                                                    }, this) : // 反面 - 使用用户选择的卡背样式\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full h-full\",\n                                                                        children: renderCardback(selectedCardback, false)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1620,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-1 -right-1 w-5 h-5 bg-amber-400 rounded-full flex items-center justify-center text-xs font-bold text-purple-900 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                                        children: isFlipped ? '↻' : '↺'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1626,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1602,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-200\",\n                                                                        children: isFlipped ? '正面' : '反面'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1633,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    imageUrl && isFlipped && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-emerald-300 mt-1\",\n                                                                        children: \"\\uD83D\\uDCF7 已上传\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1637,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1632,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1592,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, card.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1591,\n                                                    columnNumber: 23\n                                                }, this);\n                                            });\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center space-x-6 bg-purple-800/30 rounded-2xl px-6 py-3 border border-amber-400/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-purple-200\",\n                                                    children: [\n                                                        \"总计: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-amber-300 font-bold\",\n                                                            children: \"78张\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1651,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1650,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-purple-200\",\n                                                    children: [\n                                                        \"已翻开: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-amber-300 font-bold\",\n                                                            children: [\n                                                                flippedCards.size,\n                                                                \"张\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1654,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1653,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-purple-200\",\n                                                    children: [\n                                                        \"有图片: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-amber-300 font-bold\",\n                                                            children: [\n                                                                (()=>{\n                                                                    const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n                                                                    return Object.keys(uploadedImages).length;\n                                                                })(),\n                                                                \"张\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1657,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1656,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1649,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 text-center text-sm text-purple-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"\\uD83D\\uDCA1 点击任意卡牌可以单独翻转 \\xb7 使用上方按钮批量控制\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"\\uD83D\\uDCF1 已上传的图片会自动显示 \\xb7 未上传的显示默认图标\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1672,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1670,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1578,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1541,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'result' && currentReading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83D\\uDD2E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1681,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"占卜结果\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1682,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: [\n                                            '你的问题: \"',\n                                            question,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1683,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1680,\n                                columnNumber: 13\n                            }, this),\n                            currentReading.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-red-400 mb-8\",\n                                children: currentReading.error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1687,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center gap-6 mb-8 max-w-6xl mx-auto \".concat(revealedCards.length === 1 ? 'justify-center' : revealedCards.length <= 3 ? 'justify-center' : 'justify-center'),\n                                        children: revealedCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-bold text-amber-300 mb-3\",\n                                                            children: getPositionName(index)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1700,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                            children: card.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: card.imageUrl,\n                                                                alt: card.name,\n                                                                className: \"w-full h-full object-cover transition-transform duration-300 scale-110 \".concat(card.orientation === '逆位' ? 'rotate-180' : ''),\n                                                                onError: ()=>{\n                                                                // 图片加载失败时会自动显示备用内容\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1707,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl\",\n                                                                children: \"\\uD83C\\uDFB4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1718,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1705,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-semibold text-white mb-1\",\n                                                            children: card.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1723,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: card.orientation\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1724,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1699,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1698,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1692,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-amber-300 mb-6 text-center\",\n                                                children: \"解读\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1731,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-purple-100 leading-relaxed whitespace-pre-line\",\n                                                children: formatText(currentReading.interpretation)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1732,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1730,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mt-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetReading,\n                                    className: \"bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                    children: \"重新占卜 ✨\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1740,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1739,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1679,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 736,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 711,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"7QpznT2giKPLWa3LIkfw+w2/mQU=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});