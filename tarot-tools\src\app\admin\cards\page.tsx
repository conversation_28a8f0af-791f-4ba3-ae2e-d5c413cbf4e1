'use client';

import { useState, useEffect } from 'react';
import { getAllCards } from '@/constants/tarot-cards';
import { TarotCard } from '@/types/tarot';

export default function CardsManagement() {
  const [cards, setCards] = useState<TarotCard[]>([]);
  const [selectedSuit, setSelectedSuit] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCards, setFilteredCards] = useState<TarotCard[]>([]);

  useEffect(() => {
    const allCards = getAllCards();
    
    // 从localStorage加载已上传的图片
    const uploadedImages = JSON.parse(localStorage.getItem('uploadedImages') || '{}');
    
    // 合并已上传的图片路径
    const cardsWithUploadedImages = allCards.map(card => ({
      ...card,
      imageUrl: uploadedImages[card.id] || card.imageUrl
    }));
    
    setCards(cardsWithUploadedImages);
    setFilteredCards(cardsWithUploadedImages);
  }, []);

  useEffect(() => {
    let filtered = cards;

    // 按花色筛选
    if (selectedSuit !== 'all') {
      filtered = filtered.filter(card => card.suit === selectedSuit);
    }

    // 按名称搜索
    if (searchTerm) {
      filtered = filtered.filter(card => 
        card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        card.nameEn.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredCards(filtered);
  }, [cards, selectedSuit, searchTerm]);

  const getSuitName = (suit: string) => {
    const suitNames: { [key: string]: string } = {
      'major_arcana': '大阿卡纳',
      'wands': '权杖',
      'cups': '圣杯',
      'swords': '宝剑',
      'pentacles': '钱币'
    };
    return suitNames[suit] || suit;
  };

  const [uploadingCards, setUploadingCards] = useState<Set<number>>(new Set());

  const handleImageUpload = async (card: TarotCard, file: File) => {
    setUploadingCards(prev => new Set(prev).add(card.id));

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('cardId', card.id.toString());
      formData.append('suit', card.suit);

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // 更新卡牌的图片URL
        setCards(prevCards =>
          prevCards.map(c =>
            c.id === card.id
              ? { ...c, imageUrl: result.imageUrl }
              : c
          )
        );
        
        // 保存到localStorage作为临时持久化
        const uploadedImages = JSON.parse(localStorage.getItem('uploadedImages') || '{}');
        uploadedImages[card.id] = result.imageUrl;
        localStorage.setItem('uploadedImages', JSON.stringify(uploadedImages));
        
        alert('图片上传成功！');
      } else {
        alert(`上传失败: ${result.error}`);
      }
    } catch (error) {
      console.error('上传错误:', error);
      alert('上传失败，请重试');
    } finally {
      setUploadingCards(prev => {
        const newSet = new Set(prev);
        newSet.delete(card.id);
        return newSet;
      });
    }
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">卡牌管理</h1>
        <p className="mt-2 text-gray-600">管理所有塔罗牌信息和图片</p>
      </div>

      {/* 筛选和搜索 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              按花色筛选
            </label>
            <select
              value={selectedSuit}
              onChange={(e) => setSelectedSuit(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="all">全部花色</option>
              <option value="major_arcana">大阿卡纳</option>
              <option value="wands">权杖</option>
              <option value="cups">圣杯</option>
              <option value="swords">宝剑</option>
              <option value="pentacles">钱币</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              搜索卡牌
            </label>
            <input
              type="text"
              placeholder="输入卡牌名称..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
        </div>
      </div>

      {/* 卡牌列表 */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            卡牌列表 ({filteredCards.length} 张)
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  卡牌
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  花色
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  图片
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCards.map((card) => (
                <tr key={card.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {card.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {card.nameEn}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      {getSuitName(card.suit)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {card.imageUrl ? (
                        <div className="flex items-center">
                          <img
                            src={card.imageUrl}
                            alt={card.name}
                            className="h-12 w-8 object-cover rounded border"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling!.style.display = 'flex';
                            }}
                          />
                          <div className="h-12 w-8 bg-gray-200 rounded border flex items-center justify-center text-gray-400 text-xs" style={{ display: 'none' }}>
                            无图
                          </div>
                          <span className="ml-2 text-sm text-green-600">✓ 已上传</span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="h-12 w-8 bg-gray-200 rounded border flex items-center justify-center text-gray-400 text-xs">
                            无图
                          </div>
                          <span className="ml-2 text-sm text-red-600">✗ 未上传</span>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <label className={`px-3 py-1 rounded text-xs cursor-pointer ${
                      uploadingCards.has(card.id)
                        ? 'bg-gray-400 text-white cursor-not-allowed'
                        : 'bg-purple-600 hover:bg-purple-700 text-white'
                    }`}>
                      {uploadingCards.has(card.id) ? '上传中...' : '上传图片'}
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        disabled={uploadingCards.has(card.id)}
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleImageUpload(card, file);
                          }
                        }}
                      />
                    </label>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
