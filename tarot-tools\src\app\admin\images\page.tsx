'use client';

import { useState, useEffect } from 'react';
import { getAllCards } from '@/constants/tarot-cards';
import { TarotCard } from '@/types/tarot';

export default function ImageManagement() {
  const [cards, setCards] = useState<TarotCard[]>([]);
  const [selectedSuit, setSelectedSuit] = useState<string>('all');
  const [filteredCards, setFilteredCards] = useState<TarotCard[]>([]);
  const [uploadingCards, setUploadingCards] = useState<Set<number>>(new Set());
  const [draggedCard, setDraggedCard] = useState<number | null>(null);

  useEffect(() => {
    const allCards = getAllCards();
    
    // 从localStorage加载已上传的图片
    const uploadedImages = JSON.parse(localStorage.getItem('uploadedImages') || '{}');
    
    // 合并已上传的图片路径
    const cardsWithUploadedImages = allCards.map(card => ({
      ...card,
      imageUrl: uploadedImages[card.id] || card.imageUrl
    }));
    
    setCards(cardsWithUploadedImages);
    setFilteredCards(cardsWithUploadedImages);
  }, []);

  useEffect(() => {
    let filtered = cards;
    if (selectedSuit !== 'all') {
      filtered = filtered.filter(card => card.suit === selectedSuit);
    }
    setFilteredCards(filtered);
  }, [cards, selectedSuit]);

  const getSuitName = (suit: string) => {
    const suitNames: { [key: string]: string } = {
      'major_arcana': '大阿卡纳',
      'wands': '权杖',
      'cups': '圣杯',
      'swords': '宝剑',
      'pentacles': '钱币'
    };
    return suitNames[suit] || suit;
  };

  const handleImageUpload = async (card: TarotCard, file: File) => {
    setUploadingCards(prev => new Set(prev).add(card.id));

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('cardId', card.id.toString());
      formData.append('suit', card.suit);

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // 更新卡牌数据
        setCards(prevCards => 
          prevCards.map(c => 
            c.id === card.id 
              ? { ...c, imageUrl: result.imageUrl }
              : c
          )
        );
        
        // 保存到localStorage作为临时持久化
        const uploadedImages = JSON.parse(localStorage.getItem('uploadedImages') || '{}');
        uploadedImages[card.id] = result.imageUrl;
        localStorage.setItem('uploadedImages', JSON.stringify(uploadedImages));
        
        alert('图片上传成功！');
      } else {
        alert(`上传失败: ${result.error}`);
      }
    } catch (error) {
      console.error('上传错误:', error);
      alert('上传失败，请重试');
    } finally {
      setUploadingCards(prev => {
        const newSet = new Set(prev);
        newSet.delete(card.id);
        return newSet;
      });
    }
  };

  const handleDrop = (e: React.DragEvent, card: TarotCard) => {
    e.preventDefault();
    setDraggedCard(null);
    
    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));
    
    if (imageFile) {
      handleImageUpload(card, imageFile);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDragEnter = (e: React.DragEvent, cardId: number) => {
    e.preventDefault();
    setDraggedCard(cardId);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDraggedCard(null);
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">图片管理</h1>
        <p className="mt-2 text-gray-600">批量管理塔罗牌图片，支持拖拽上传</p>
      </div>

      {/* 筛选器 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <div className="flex items-center space-x-4">
          <label className="block text-sm font-medium text-gray-700">
            按花色筛选:
          </label>
          <select
            value={selectedSuit}
            onChange={(e) => setSelectedSuit(e.target.value)}
            className="block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="all">全部花色</option>
            <option value="major_arcana">大阿卡纳</option>
            <option value="wands">权杖</option>
            <option value="cups">圣杯</option>
            <option value="swords">宝剑</option>
            <option value="pentacles">钱币</option>
          </select>
        </div>
      </div>

      {/* 图片网格 */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {filteredCards.map((card) => (
          <div
            key={card.id}
            className={`bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200 ${
              draggedCard === card.id ? 'ring-2 ring-purple-500 scale-105' : ''
            } ${uploadingCards.has(card.id) ? 'opacity-50' : ''}`}
            onDrop={(e) => handleDrop(e, card)}
            onDragOver={handleDragOver}
            onDragEnter={(e) => handleDragEnter(e, card.id)}
            onDragLeave={handleDragLeave}
          >
            {/* 图片区域 */}
            <div className="aspect-[2/3] bg-gray-100 relative">
              {card.imageUrl ? (
                <img
                  src={card.imageUrl}
                  alt={card.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling!.style.display = 'flex';
                  }}
                />
              ) : null}
              
              {/* 占位符 */}
              <div 
                className={`absolute inset-0 flex flex-col items-center justify-center text-gray-400 ${
                  card.imageUrl ? 'hidden' : 'flex'
                }`}
              >
                <div className="text-2xl mb-2">🎴</div>
                <div className="text-xs text-center px-2">拖拽图片到此处</div>
              </div>

              {/* 上传中遮罩 */}
              {uploadingCards.has(card.id) && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="text-white text-sm">上传中...</div>
                </div>
              )}

              {/* 拖拽提示 */}
              {draggedCard === card.id && (
                <div className="absolute inset-0 bg-purple-500 bg-opacity-20 border-2 border-purple-500 border-dashed flex items-center justify-center">
                  <div className="text-purple-700 font-medium">释放以上传</div>
                </div>
              )}
            </div>

            {/* 卡牌信息 */}
            <div className="p-3">
              <div className="text-sm font-medium text-gray-900 truncate">
                {card.name}
              </div>
              <div className="text-xs text-gray-500 truncate">
                {card.nameEn}
              </div>
              <div className="mt-2">
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                  {getSuitName(card.suit)}
                </span>
              </div>
              
              {/* 上传按钮 */}
              <div className="mt-3">
                <label className={`block w-full text-center px-2 py-1 rounded text-xs cursor-pointer ${
                  uploadingCards.has(card.id)
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-purple-600 hover:bg-purple-700 text-white'
                }`}>
                  {card.imageUrl ? '更换图片' : '上传图片'}
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    disabled={uploadingCards.has(card.id)}
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        handleImageUpload(card, file);
                      }
                    }}
                  />
                </label>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 使用说明 */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">使用说明:</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• 支持 JPG、PNG、WebP、SVG 格式图片</li>
          <li>• 文件大小不超过 5MB</li>
          <li>• 可以直接拖拽图片到卡牌上进行上传</li>
          <li>• 建议图片比例为 2:3 (宽:高)</li>
        </ul>
      </div>
    </div>
  );
}
