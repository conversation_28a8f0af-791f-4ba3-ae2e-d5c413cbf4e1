'use client';

import { useState, useEffect } from 'react';
import { getAllCards } from '@/constants/tarot-cards';

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalCards: 0,
    cardsWithImages: 0,
    cardsWithoutImages: 0,
    imageProgress: 0
  });

  useEffect(() => {
    const allCards = getAllCards();
    const cardsWithImages = allCards.filter(card => {
      // 检查是否有图片文件存在（这里简化处理）
      return card.imageUrl && card.imageUrl !== '';
    });

    setStats({
      totalCards: allCards.length,
      cardsWithImages: cardsWithImages.length,
      cardsWithoutImages: allCards.length - cardsWithImages.length,
      imageProgress: Math.round((cardsWithImages.length / allCards.length) * 100)
    });
  }, []);

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">管理仪表板</h1>
        <p className="mt-2 text-gray-600">塔罗牌系统概览</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">🎴</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    总卡牌数
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.totalCards}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">✅</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    已有图片
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.cardsWithImages}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">❌</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    缺少图片
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.cardsWithoutImages}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl">📊</div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    完成度
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.imageProgress}%
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 进度条 */}
      <div className="bg-white shadow rounded-lg p-6 mb-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">图片上传进度</h3>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div 
            className="bg-purple-600 h-2.5 rounded-full transition-all duration-300"
            style={{ width: `${stats.imageProgress}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          已完成 {stats.cardsWithImages} / {stats.totalCards} 张卡牌图片
        </p>
      </div>

      {/* 快速操作 */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a
            href="/admin/cards"
            className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="text-2xl mb-2">🎴</div>
            <h4 className="font-medium text-gray-900">管理卡牌</h4>
            <p className="text-sm text-gray-600">查看和编辑所有塔罗牌信息</p>
          </a>
          
          <a
            href="/admin/images"
            className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="text-2xl mb-2">🖼️</div>
            <h4 className="font-medium text-gray-900">图片管理</h4>
            <p className="text-sm text-gray-600">上传和管理卡牌图片</p>
          </a>
          
          <a
            href="/"
            target="_blank"
            className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="text-2xl mb-2">🔮</div>
            <h4 className="font-medium text-gray-900">查看网站</h4>
            <p className="text-sm text-gray-600">在新窗口中打开塔罗牌网站</p>
          </a>
        </div>
      </div>
    </div>
  );
}
