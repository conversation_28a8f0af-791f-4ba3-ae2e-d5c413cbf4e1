<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 塔罗牌卡背处理工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .preview-area {
            display: none;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .preview-box {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f9f9f9;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .slider-container {
            margin: 20px 0;
        }
        
        .slider {
            width: 100%;
            margin: 10px 0;
        }
        
        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .crop-overlay {
            position: absolute;
            border: 2px solid #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            cursor: move;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 塔罗牌卡背处理工具</h1>
            <p>去除多余区域 · 自动裁剪 · 尺寸调整</p>
        </div>

        <div class="main-content">
            <!-- 上传区域 -->
            <div class="upload-area" id="uploadArea">
                <div style="font-size: 3em; margin-bottom: 15px;">🎴</div>
                <div style="font-size: 1.2em; margin-bottom: 15px;">拖拽卡背图片到这里</div>
                <div style="color: #666; font-size: 0.9em; margin-bottom: 15px;">
                    支持 JPG、PNG 格式 | 自动去除多余区域
                </div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    选择图片文件
                </button>
            </div>

            <!-- 预览和处理区域 -->
            <div class="preview-area" id="previewArea">
                <div class="preview-box">
                    <h3>📷 原始图片</h3>
                    <canvas id="originalCanvas"></canvas>
                    <div class="canvas-container">
                        <div class="crop-overlay" id="cropOverlay"></div>
                    </div>
                </div>
                
                <div class="preview-box">
                    <h3>✂️ 处理结果</h3>
                    <canvas id="resultCanvas"></canvas>
                </div>
            </div>

            <!-- 控制面板 -->}
            <div class="controls" id="controls" style="display: none;">
                <h3>🔧 处理选项</h3>
                
                <div class="slider-container">
                    <label>裁剪模式：</label>
                    <div style="margin: 10px 0;">
                        <button class="btn" onclick="autoCropWhite()">🤖 自动去白边</button>
                        <button class="btn" onclick="cropToTarotSize()">📏 裁剪到塔罗牌尺寸</button>
                        <button class="btn" onclick="removeBackground()">🎨 智能去背景</button>
                    </div>
                </div>
                
                <div class="slider-container">
                    <label>调整边距：</label>
                    <input type="range" class="slider" id="marginSlider" min="0" max="50" value="10" onchange="updateMargin()">
                    <div>边距：<span id="marginValue">10</span>px</div>
                </div>
                
                <div class="slider-container">
                    <label>图片质量：</label>
                    <input type="range" class="slider" id="qualitySlider" min="70" max="100" value="90" onchange="updateQuality()">
                    <div>质量：<span id="qualityValue">90</span>%</div>
                </div>
                
                <div style="margin-top: 20px;">
                    <button class="btn" onclick="downloadResult()">📥 下载处理结果</button>
                    <button class="btn" onclick="copyToProject()">🚀 应用到项目</button>
                </div>
            </div>

            <div id="statusMessage"></div>
        </div>
    </div>

    <script>
        let originalImage = null;
        let processedCanvas = null;
        let margin = 10;
        let quality = 90;

        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#667eea';
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#ddd';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                loadImage(files[0]);
            }
        }

        function handleFileSelect(e) {
            if (e.target.files.length > 0) {
                loadImage(e.target.files[0]);
            }
        }

        function loadImage(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                originalImage = new Image();
                originalImage.onload = function() {
                    displayOriginalImage();
                    document.getElementById('previewArea').style.display = 'grid';
                    document.getElementById('controls').style.display = 'block';
                    showStatus(`图片加载成功：${file.name}`, 'success');
                };
                originalImage.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function displayOriginalImage() {
            const canvas = document.getElementById('originalCanvas');
            const ctx = canvas.getContext('2d');
            
            // 计算显示尺寸
            const maxWidth = 300;
            const maxHeight = 400;
            const scale = Math.min(maxWidth / originalImage.width, maxHeight / originalImage.height);
            
            canvas.width = originalImage.width * scale;
            canvas.height = originalImage.height * scale;
            
            ctx.drawImage(originalImage, 0, 0, canvas.width, canvas.height);
        }

        function autoCropWhite() {
            if (!originalImage) return;
            
            showStatus('正在自动检测和去除白边...', 'info');
            
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = originalImage.width;
            tempCanvas.height = originalImage.height;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(originalImage, 0, 0);
            
            const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
            const data = imageData.data;
            
            let minX = tempCanvas.width, minY = tempCanvas.height, maxX = 0, maxY = 0;
            
            // 寻找非白色像素的边界
            for (let y = 0; y < tempCanvas.height; y++) {
                for (let x = 0; x < tempCanvas.width; x++) {
                    const index = (y * tempCanvas.width + x) * 4;
                    const r = data[index];
                    const g = data[index + 1];
                    const b = data[index + 2];
                    const a = data[index + 3];
                    
                    // 检查是否为非白色像素（考虑透明度）
                    if (a > 10 && (r < 240 || g < 240 || b < 240)) {
                        minX = Math.min(minX, x);
                        maxX = Math.max(maxX, x);
                        minY = Math.min(minY, y);
                        maxY = Math.max(maxY, y);
                    }
                }
            }
            
            // 添加边距
            minX = Math.max(0, minX - margin);
            minY = Math.max(0, minY - margin);
            maxX = Math.min(tempCanvas.width, maxX + margin);
            maxY = Math.min(tempCanvas.height, maxY + margin);
            
            // 裁剪并调整到塔罗牌尺寸
            const cropWidth = maxX - minX;
            const cropHeight = maxY - minY;
            
            if (cropWidth > 0 && cropHeight > 0) {
                createProcessedImage(minX, minY, cropWidth, cropHeight);
                showStatus('白边去除完成！', 'success');
            } else {
                showStatus('未能检测到有效内容区域', 'error');
            }
        }

        function cropToTarotSize() {
            if (!originalImage) return;
            
            showStatus('正在调整到塔罗牌标准尺寸...', 'info');
            
            const targetRatio = 1030 / 1795; // 塔罗牌比例
            const sourceRatio = originalImage.width / originalImage.height;
            
            let cropX, cropY, cropWidth, cropHeight;
            
            if (sourceRatio > targetRatio) {
                // 图片太宽，按高度裁剪
                cropHeight = originalImage.height;
                cropWidth = originalImage.height * targetRatio;
                cropX = (originalImage.width - cropWidth) / 2;
                cropY = 0;
            } else {
                // 图片太高，按宽度裁剪
                cropWidth = originalImage.width;
                cropHeight = originalImage.width / targetRatio;
                cropX = 0;
                cropY = (originalImage.height - cropHeight) / 2;
            }
            
            createProcessedImage(cropX, cropY, cropWidth, cropHeight);
            showStatus('塔罗牌尺寸调整完成！', 'success');
        }

        function removeBackground() {
            showStatus('智能去背景功能需要使用在线工具：Remove.bg', 'info');
            window.open('https://www.remove.bg/', '_blank');
        }

        function createProcessedImage(cropX, cropY, cropWidth, cropHeight) {
            const resultCanvas = document.getElementById('resultCanvas');
            const ctx = resultCanvas.getContext('2d');
            
            // 设置目标尺寸
            const targetWidth = 1030;
            const targetHeight = 1795;
            
            resultCanvas.width = targetWidth;
            resultCanvas.height = targetHeight;
            
            // 清空画布
            ctx.clearRect(0, 0, targetWidth, targetHeight);
            
            // 绘制裁剪后的图片
            ctx.drawImage(
                originalImage,
                cropX, cropY, cropWidth, cropHeight,
                0, 0, targetWidth, targetHeight
            );
            
            processedCanvas = resultCanvas;
        }

        function updateMargin() {
            const slider = document.getElementById('marginSlider');
            const value = document.getElementById('marginValue');
            margin = slider.value;
            value.textContent = margin;
        }

        function updateQuality() {
            const slider = document.getElementById('qualitySlider');
            const value = document.getElementById('qualityValue');
            quality = slider.value;
            value.textContent = quality;
        }

        function downloadResult() {
            if (!processedCanvas) {
                showStatus('请先处理图片', 'error');
                return;
            }
            
            processedCanvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'tarot-cardback-1030x1795.jpg';
                a.click();
                URL.revokeObjectURL(url);
                showStatus('图片已下载！', 'success');
            }, 'image/jpeg', quality / 100);
        }

        function copyToProject() {
            if (!processedCanvas) {
                showStatus('请先处理图片', 'error');
                return;
            }
            
            showStatus('请将下载的图片文件放到 tarot-tools/public/images/ 文件夹中，然后重命名为 cardback-ai.jpg', 'info');
            downloadResult();
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }
    </script>
</body>
</html>
