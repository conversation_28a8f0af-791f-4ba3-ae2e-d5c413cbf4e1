/**
 * 塔罗牌解读工具
 * 提供智能的塔罗牌解读功能
 */

import { 
  DrawnCard, 
  TarotReading, 
  SpreadConfig, 
  ReadingType, 
  TarotOrientation, 
  InterpretationOptions,
  TarotSuit 
} from '@/types/tarot';
import { SpreadManager } from './tarot-spreads';

/**
 * 解读生成器
 */
export class TarotInterpreter {
  private readingType: ReadingType;
  private spreadConfig: SpreadConfig;
  private options: InterpretationOptions;

  constructor(
    readingType: ReadingType,
    spreadConfig: SpreadConfig,
    options: InterpretationOptions = {
      includePositions: true,
      includeReversed: true,
      detailLevel: 'detailed',
      readingType
    }
  ) {
    this.readingType = readingType;
    this.spreadConfig = spreadConfig;
    this.options = options;
  }

  /**
   * 生成完整的塔罗牌解读
   */
  generateReading(
    drawnCards: DrawnCard[],
    question: string = ''
  ): TarotReading {
    // 验证牌数
    if (drawnCards.length !== this.spreadConfig.cardCount) {
      throw new Error(`牌数不匹配：期望 ${this.spreadConfig.cardCount} 张，实际 ${drawnCards.length} 张`);
    }

    // 生成解读内容
    const interpretation = this.interpretCards(drawnCards, question);

    return {
      id: this.generateReadingId(),
      question,
      spreadType: this.spreadConfig.type,
      drawnCards,
      interpretation,
      readingType: this.readingType,
      createdAt: new Date(),
      isPublic: false,
      isFavorite: false
    };
  }

  /**
   * 解读牌组
   */
  private interpretCards(drawnCards: DrawnCard[], question: string): string {
    let interpretation = '';

    // 添加开场白
    interpretation += this.generateOpening(question);

    // 逐张解读
    if (this.options.detailLevel === 'comprehensive') {
      interpretation += this.generateDetailedInterpretation(drawnCards);
    } else {
      interpretation += this.generateBasicInterpretation(drawnCards);
    }

    // 添加整体总结
    interpretation += this.generateSummary(drawnCards);

    // 添加建议
    interpretation += this.generateAdvice(drawnCards);

    return interpretation;
  }

  /**
   * 生成开场白
   */
  private generateOpening(question: string): string {
    const readingTypeNames = {
      [ReadingType.LOVE]: '爱情',
      [ReadingType.CAREER]: '事业',
      [ReadingType.HEALTH]: '健康',
      [ReadingType.FINANCE]: '财运',
      [ReadingType.GENERAL]: '综合',
      [ReadingType.SPIRITUAL]: '精神成长',
      [ReadingType.RELATIONSHIP]: '人际关系'
    };

    let opening = `## ${readingTypeNames[this.readingType]}占卜解读\n\n`;
    
    if (question) {
      opening += `**你的问题：** ${question}\n\n`;
    }
    
    opening += `**使用牌阵：** ${this.spreadConfig.name}\n\n`;
    opening += `${this.spreadConfig.description}\n\n`;
    opening += `---\n\n`;

    return opening;
  }

  /**
   * 生成基础解读
   */
  private generateBasicInterpretation(drawnCards: DrawnCard[]): string {
    let interpretation = '## 牌面解读\n\n';

    for (const drawnCard of drawnCards) {
      const position = this.spreadConfig.positions.find(p => p.id === drawnCard.position + 1);
      const positionName = position?.name || `位置 ${drawnCard.position + 1}`;
      const positionMeaning = position?.meaning || '';

      interpretation += `### ${positionName}\n\n`;
      
      if (this.options.includePositions && positionMeaning) {
        interpretation += `**位置含义：** ${positionMeaning}\n\n`;
      }

      interpretation += `**抽到的牌：** ${drawnCard.card.name}`;
      
      if (this.options.includeReversed && drawnCard.orientation === TarotOrientation.REVERSED) {
        interpretation += ' (逆位)';
      }
      
      interpretation += '\n\n';

      // 添加牌面含义
      const meaning = this.getCardMeaning(drawnCard);
      interpretation += `**含义：** ${meaning}\n\n`;

      // 添加关键词
      const keywords = this.getCardKeywords(drawnCard);
      interpretation += `**关键词：** ${keywords.join('、')}\n\n`;

      // 根据占卜类型和位置提供特定解读
      const contextualMeaning = this.generateContextualMeaning(drawnCard, position);
      if (contextualMeaning) {
        interpretation += `**在此情况下：** ${contextualMeaning}\n\n`;
      }

      interpretation += '---\n\n';
    }

    return interpretation;
  }

  /**
   * 生成详细解读
   */
  private generateDetailedInterpretation(drawnCards: DrawnCard[]): string {
    let interpretation = this.generateBasicInterpretation(drawnCards);

    // 添加牌组相互关系分析
    interpretation += '## 牌组关系分析\n\n';
    interpretation += this.analyzeCardRelationships(drawnCards);
    interpretation += '\n\n';

    // 添加数字学分析
    interpretation += '## 数字学洞察\n\n';
    interpretation += this.analyzeNumerology(drawnCards);
    interpretation += '\n\n';

    // 添加元素分析
    interpretation += '## 元素平衡\n\n';
    interpretation += this.analyzeElements(drawnCards);
    interpretation += '\n\n';

    return interpretation;
  }

  /**
   * 获取牌的含义
   */
  private getCardMeaning(drawnCard: DrawnCard): string {
    if (this.options.includeReversed && drawnCard.orientation === TarotOrientation.REVERSED) {
      return drawnCard.card.reversedMeaning;
    }
    return drawnCard.card.uprightMeaning;
  }

  /**
   * 获取牌的关键词
   */
  private getCardKeywords(drawnCard: DrawnCard): string[] {
    if (this.options.includeReversed && drawnCard.orientation === TarotOrientation.REVERSED) {
      return drawnCard.card.reversedKeywords;
    }
    return drawnCard.card.uprightKeywords;
  }

  /**
   * 生成基于上下文的含义
   */
  private generateContextualMeaning(drawnCard: DrawnCard, position: any): string {
    const card = drawnCard.card;
    const isReversed = drawnCard.orientation === TarotOrientation.REVERSED;
    
    // 基于占卜类型的特定解读
    switch (this.readingType) {
      case ReadingType.LOVE:
        return this.generateLoveContextMeaning(card, isReversed, position);
      case ReadingType.CAREER:
        return this.generateCareerContextMeaning(card, isReversed, position);
      case ReadingType.HEALTH:
        return this.generateHealthContextMeaning(card, isReversed, position);
      case ReadingType.FINANCE:
        return this.generateFinanceContextMeaning(card, isReversed, position);
      default:
        return '';
    }
  }

  /**
   * 爱情相关的上下文解读
   */
  private generateLoveContextMeaning(card: any, isReversed: boolean, position: any): string {
    // 简化版本，实际实现可以更复杂
    if (card.suit === TarotSuit.CUPS) {
      return isReversed 
        ? '在感情方面可能遇到情感阻碍或沟通问题'
        : '感情生活充满温暖和爱意';
    }
    
    if (card.suit === TarotSuit.SWORDS) {
      return isReversed 
        ? '感情冲突有望得到解决'
        : '可能面临感情上的挑战或分歧';
    }

    return '';
  }

  /**
   * 事业相关的上下文解读
   */
  private generateCareerContextMeaning(card: any, isReversed: boolean, position: any): string {
    if (card.suit === TarotSuit.WANDS) {
      return isReversed 
        ? '事业发展可能暂时受阻，需要重新规划'
        : '事业充满活力和创新机会';
    }

    if (card.suit === TarotSuit.PENTACLES) {
      return isReversed 
        ? '财务状况需要谨慎管理'
        : '物质和财务方面有稳定的收获';
    }

    return '';
  }

  /**
   * 健康相关的上下文解读
   */
  private generateHealthContextMeaning(card: any, isReversed: boolean, position: any): string {
    // 简化实现
    return isReversed 
      ? '需要特别关注身心健康，适当休息'
      : '整体健康状况良好，保持现有的生活方式';
  }

  /**
   * 财运相关的上下文解读
   */
  private generateFinanceContextMeaning(card: any, isReversed: boolean, position: any): string {
    if (card.suit === TarotSuit.PENTACLES) {
      return isReversed 
        ? '财务管理需要更加谨慎，避免不必要的支出'
        : '财运亨通，投资和理财都有良好的前景';
    }

    return '';
  }

  /**
   * 分析牌组关系
   */
  private analyzeCardRelationships(drawnCards: DrawnCard[]): string {
    let analysis = '';

    // 分析花色分布
    const suitCounts = this.countSuits(drawnCards);
    const dominantSuit = this.getDominantSuit(suitCounts);
    
    if (dominantSuit) {
      analysis += this.getSuitMeaning(dominantSuit, suitCounts[dominantSuit]);
    }

    // 分析正逆位比例
    const orientationAnalysis = this.analyzeOrientation(drawnCards);
    analysis += orientationAnalysis;

    return analysis;
  }

  /**
   * 计算花色分布
   */
  private countSuits(drawnCards: DrawnCard[]): Record<TarotSuit, number> {
    const counts: Record<TarotSuit, number> = {
      [TarotSuit.MAJOR_ARCANA]: 0,
      [TarotSuit.WANDS]: 0,
      [TarotSuit.CUPS]: 0,
      [TarotSuit.SWORDS]: 0,
      [TarotSuit.PENTACLES]: 0
    };

    for (const drawnCard of drawnCards) {
      counts[drawnCard.card.suit]++;
    }

    return counts;
  }

  /**
   * 获取主导花色
   */
  private getDominantSuit(suitCounts: Record<TarotSuit, number>): TarotSuit | null {
    let maxCount = 0;
    let dominantSuit: TarotSuit | null = null;

    for (const [suit, count] of Object.entries(suitCounts)) {
      if (count > maxCount && count > 1) {
        maxCount = count;
        dominantSuit = suit as TarotSuit;
      }
    }

    return dominantSuit;
  }

  /**
   * 获取花色含义
   */
  private getSuitMeaning(suit: TarotSuit, count: number): string {
    const meanings = {
      [TarotSuit.MAJOR_ARCANA]: `${count}张大阿卡纳牌显示这个问题具有重大的精神意义和深远影响。`,
      [TarotSuit.WANDS]: `${count}张权杖牌表明创造力、热情和行动力是关键因素。`,
      [TarotSuit.CUPS]: `${count}张圣杯牌突出了情感、直觉和人际关系的重要性。`,
      [TarotSuit.SWORDS]: `${count}张宝剑牌指向思维、沟通和挑战需要特别关注。`,
      [TarotSuit.PENTACLES]: `${count}张星币牌强调物质、工作和实际事务的影响。`
    };

    return meanings[suit] + '\n\n';
  }

  /**
   * 分析正逆位
   */
  private analyzeOrientation(drawnCards: DrawnCard[]): string {
    if (!this.options.includeReversed) return '';

    const reversedCount = drawnCards.filter(card => 
      card.orientation === TarotOrientation.REVERSED
    ).length;

    const totalCount = drawnCards.length;
    const reversedRatio = reversedCount / totalCount;

    if (reversedRatio > 0.6) {
      return '大量逆位牌提示需要内省和反思，可能存在内在阻碍需要克服。\n\n';
    } else if (reversedRatio === 0) {
      return '全部正位牌显示能量流动顺畅，是积极行动的好时机。\n\n';
    }

    return '';
  }

  /**
   * 数字学分析
   */
  private analyzeNumerology(drawnCards: DrawnCard[]): string {
    const numbers = drawnCards
      .map(card => card.card.numerology)
      .filter(num => num !== undefined) as number[];

    if (numbers.length === 0) return '此次抽牌主要为宫廷牌，强调人物和性格特质的影响。';

    const total = numbers.reduce((sum, num) => sum + num, 0);
    const average = total / numbers.length;

    let analysis = `数字总和为 ${total}，平均值为 ${average.toFixed(1)}。`;

    if (average < 5) {
      analysis += '较低的数字能量表明新的开始和基础建设的重要性。';
    } else if (average > 15) {
      analysis += '较高的数字能量显示成熟和完成的主题。';
    } else {
      analysis += '中等的数字能量表明发展和成长的过程。';
    }

    return analysis;
  }

  /**
   * 元素分析
   */
  private analyzeElements(drawnCards: DrawnCard[]): string {
    const elements: Record<string, number> = {
      '火': 0,
      '水': 0,
      '风': 0,
      '土': 0
    };

    for (const drawnCard of drawnCards) {
      const element = drawnCard.card.element;
      if (element && elements[element] !== undefined) {
        elements[element]++;
      }
    }

    const dominantElement = Object.entries(elements)
      .sort(([,a], [,b]) => b - a)[0];

    if (dominantElement[1] > 1) {
      const meanings = {
        '火': '火元素主导，强调激情、创造力和行动力。',
        '水': '水元素主导，突出情感、直觉和流动性。',
        '风': '风元素主导，强调思维、沟通和变化。',
        '土': '土元素主导，注重实际、稳定和物质层面。'
      };

      return meanings[dominantElement[0] as keyof typeof meanings];
    }

    return '元素分布平衡，各个层面都需要关注。';
  }

  /**
   * 生成总结
   */
  private generateSummary(drawnCards: DrawnCard[]): string {
    let summary = '## 整体总结\n\n';
    
    // 基于牌阵类型的总结
    switch (this.spreadConfig.type) {
      case 'three_card':
        summary += this.generateThreeCardSummary(drawnCards);
        break;
      case 'celtic_cross':
        summary += this.generateCelticCrossSummary(drawnCards);
        break;
      default:
        summary += this.generateGeneralSummary(drawnCards);
    }

    return summary;
  }

  /**
   * 三张牌总结
   */
  private generateThreeCardSummary(drawnCards: DrawnCard[]): string {
    return '从过去到未来的发展脉络清晰可见。过去的影响为现在的状况奠定了基础，' +
           '而现在的选择和行动将直接影响未来的发展方向。\n\n';
  }

  /**
   * 凯尔特十字总结
   */
  private generateCelticCrossSummary(drawnCards: DrawnCard[]): string {
    return '凯尔特十字为你提供了全面的洞察。内在和外在的影响因素都已明确，' +
           '你的态度和行动方式将决定最终的结果。\n\n';
  }

  /**
   * 一般总结
   */
  private generateGeneralSummary(drawnCards: DrawnCard[]): string {
    return '牌面显示的信息为你当前的问题提供了重要的指引和洞察。\n\n';
  }

  /**
   * 生成建议
   */
  private generateAdvice(drawnCards: DrawnCard[]): string {
    let advice = '## 行动建议\n\n';
    
    // 基于占卜类型的建议
    switch (this.readingType) {
      case ReadingType.LOVE:
        advice += '在感情方面，保持开放和诚实的沟通。倾听内心的声音，同时也要考虑对方的感受。';
        break;
      case ReadingType.CAREER:
        advice += '在事业发展上，制定清晰的目标并付诸行动。保持专业态度，积极寻求成长机会。';
        break;
      case ReadingType.HEALTH:
        advice += '关注身心健康的平衡。适当的休息和运动同样重要，不要忽视心理健康。';
        break;
      case ReadingType.FINANCE:
        advice += '在财务管理上要谨慎理性。制定预算计划，避免冲动消费，寻求稳健的投资机会。';
        break;
      default:
        advice += '保持积极的心态，相信自己的直觉和判断。每个挑战都是成长的机会。';
    }

    advice += '\n\n记住，塔罗牌提供的是指引而非绝对的预言。最终的选择和行动权始终在你手中。\n\n';
    
    return advice;
  }

  /**
   * 生成解读ID
   */
  private generateReadingId(): string {
    return `reading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 便捷的解读生成器
 */
export const interpretationHelper = {
  /**
   * 快速解读
   */
  quickInterpret: (
    drawnCards: DrawnCard[],
    spreadType: any,
    readingType: ReadingType = ReadingType.GENERAL,
    question: string = ''
  ): string => {
    const spread = SpreadManager.getSpread(spreadType);
    const interpreter = new TarotInterpreter(readingType, spread, {
      includePositions: true,
      includeReversed: true,
      detailLevel: 'brief',
      readingType
    });

    const reading = interpreter.generateReading(drawnCards, question);
    return reading.interpretation;
  },

  /**
   * 详细解读
   */
  detailedInterpret: (
    drawnCards: DrawnCard[],
    spreadType: any,
    readingType: ReadingType = ReadingType.GENERAL,
    question: string = ''
  ): TarotReading => {
    const spread = SpreadManager.getSpread(spreadType);
    const interpreter = new TarotInterpreter(readingType, spread, {
      includePositions: true,
      includeReversed: true,
      detailLevel: 'comprehensive',
      readingType
    });

    return interpreter.generateReading(drawnCards, question);
  }
};
