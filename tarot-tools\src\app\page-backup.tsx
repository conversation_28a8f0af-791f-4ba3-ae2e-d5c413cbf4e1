'use client';

import { useState, useEffect } from 'react';
import TarotCard from '@/components/TarotCard';
import SpreadLayout from '@/components/SpreadLayouts';
import { quickDemo } from '@/utils/tarot-demo';

export default function Home() {
  const [currentStep, setCurrentStep] = useState<'select' | 'question' | 'spread' | 'shuffle' | 'draw' | 'result'>('select');
  const [selectedType, setSelectedType] = useState<'single' | 'three' | 'cross' | 'celtic' | 'love' | 'career' | null>(null);
  const [question, setQuestion] = useState('');

  const startReading = (type: 'single' | 'three' | 'cross' | 'celtic' | 'love' | 'career') => {
    setSelectedType(type);
    setCurrentStep('question');
  };

  const resetReading = () => {
    setCurrentStep('select');
    setSelectedType(null);
    setQuestion('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950 text-white overflow-hidden relative">
      {/* 星空背景 */}
      <div className="absolute inset-0">
        {/* 主要星星 */}
        <div className="absolute top-[10%] left-[15%] w-1 h-1 bg-amber-300 rounded-full animate-pulse shadow-lg shadow-amber-300/50"></div>
        <div className="absolute top-[25%] right-[20%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-1000 shadow-sm shadow-amber-200/30"></div>
        <div className="absolute top-[40%] left-[8%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-500 shadow-lg shadow-amber-400/60"></div>
        <div className="absolute top-[60%] right-[12%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1500"></div>
        <div className="absolute top-[75%] left-[25%] w-1 h-1 bg-amber-200 rounded-full animate-pulse delay-2000 shadow-md shadow-amber-200/40"></div>
        <div className="absolute top-[35%] right-[35%] w-0.5 h-0.5 bg-amber-400 rounded-full animate-pulse delay-700"></div>
        <div className="absolute top-[80%] right-[40%] w-1 h-1 bg-amber-300 rounded-full animate-pulse delay-1200 shadow-lg shadow-amber-300/50"></div>
        <div className="absolute top-[15%] left-[60%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-800"></div>
        <div className="absolute top-[50%] left-[70%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-300 shadow-lg shadow-amber-400/60"></div>
        <div className="absolute top-[90%] left-[45%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1800"></div>
        
        {/* 微小星点 */}
        <div className="absolute top-[20%] left-[30%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-60 animate-pulse delay-2500"></div>
        <div className="absolute top-[70%] right-[60%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-40 animate-pulse delay-3000"></div>
        <div className="absolute top-[45%] left-[85%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-50 animate-pulse delay-2200"></div>
      </div>
      
      {/* 渐变光晕 */}
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-amber-400/5 rounded-full blur-3xl"></div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* 头部导航 */}
        {currentStep !== 'select' && (
          <div className="flex justify-between items-center mb-12">
            <button
              onClick={resetReading}
              className="group flex items-center space-x-3 bg-gradient-to-r from-purple-800/30 to-violet-800/30 hover:from-purple-700/40 hover:to-violet-700/40 backdrop-blur-md rounded-2xl px-8 py-4 transition-all duration-300 border border-amber-400/20 hover:border-amber-400/40 shadow-lg hover:shadow-amber-400/10"
            >
              <span className="text-amber-300 group-hover:text-amber-200 transition-colors text-lg">←</span>
              <span className="text-amber-100 group-hover:text-white transition-colors font-medium">重新开始</span>
            </button>
            
            {/* 进度指示器 */}
            <div className="flex items-center space-x-6">
              <div className="flex space-x-2">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-3 h-3 rounded-full transition-all duration-500 ${
                      i <= ['select', 'question', 'spread', 'shuffle', 'draw', 'result'].indexOf(currentStep)
                        ? 'bg-gradient-to-r from-amber-400 to-amber-300 shadow-lg shadow-amber-400/50'
                        : 'bg-purple-300/20 border border-purple-300/30'
                    }`}
                  ></div>
                ))}
              </div>
              <div className="text-sm text-amber-200 font-medium bg-purple-800/20 px-4 py-2 rounded-full border border-amber-400/20">
                {{'select': '✨ 选择类型', 'question': '💭 输入问题', 'spread': '🔮 准备占卜', 'shuffle': '🌀 洗牌', 'draw': '🎴 选牌', 'result': '📜 解读结果'}[currentStep]}
              </div>
            </div>
            
            <div className="w-32"></div> {/* 占位符保持居中 */}
          </div>
        )}

        {currentStep === 'select' && (
          <div className="max-w-7xl mx-auto">
            {/* 主标题 */}
            <div className="text-center mb-20">
              <div className="relative inline-block mb-12">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-purple-400/20 blur-3xl rounded-full"></div>
                <div className="relative text-8xl mb-6 animate-pulse">🔮</div>
                <div className="absolute -top-2 -right-2 text-2xl animate-spin-slow">✨</div>
                <div className="absolute -bottom-2 -left-2 text-xl animate-bounce">🌙</div>
              </div>

              <h1 className="text-6xl font-bold mb-8 bg-gradient-to-r from-amber-300 via-amber-200 to-amber-100 bg-clip-text text-transparent tracking-wider drop-shadow-lg">
                神秘塔罗
              </h1>
              <p className="text-xl text-purple-200 mb-12 tracking-wide font-light">
                ✨ 探索命运的奥秘 · 窥见未来的真相 ✨
              </p>

              <div className="max-w-5xl mx-auto bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-10 border border-amber-400/20 shadow-2xl shadow-purple-900/20">
                <h2 className="text-2xl font-semibold mb-8 text-amber-300 text-center">✨ 塔罗占卜指引 ✨</h2>
                <div className="grid md:grid-cols-2 gap-8 text-left">
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">🎯</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">专注你的问题</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">在内心深处思考你想要了解的事情，让宇宙感受到你的诚意</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">🌀</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">神秘洗牌仪式</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">观看古老的塔罗牌洗牌过程，感受神秘力量的流动</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">👆</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">凭直觉选牌</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">从78张牌中选择命运为你准备的牌，相信你的第一感觉</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">📜</div>
                    <div>
                      <h3 className="font-semibold text-amber-100 mb-2">专业解读</h3>
                      <p className="text-purple-200 text-sm leading-relaxed">获得深度的塔罗牌解读和人生指引，照亮前行的道路</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 占卜类型选择 */}
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-16 text-amber-300">🔮 选择你的占卜类型 🔮</h2>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    type: 'single',
                    icon: '🌟',
                    title: '单牌指引',
                    desc: '简单直接的答案',
                    tag: '1张牌 · 初级',
                    color: 'from-amber-400 to-amber-600'
                  },
                  {
                    type: 'three',
                    icon: '⏳',
                    title: '时间三牌',
                    desc: '过去-现在-未来',
                    tag: '3张牌 · 推荐',
                    color: 'from-purple-400 to-purple-600'
                  },
                  {
                    type: 'cross',
                    icon: '✚',
                    title: '十字占卜',
                    desc: '核心-影响-结果',
                    tag: '5张牌 · 中级',
                    color: 'from-violet-400 to-violet-600'
                  },
                  {
                    type: 'celtic',
                    icon: '🎯',
                    title: '凯尔特十字',
                    desc: '最详细的全面解读',
                    tag: '10张牌 · 高级',
                    color: 'from-indigo-400 to-indigo-600'
                  },
                  {
                    type: 'love',
                    icon: '💕',
                    title: '爱情解读',
                    desc: '专门的情感占卜',
                    tag: '3张牌 · 专题',
                    color: 'from-pink-400 to-pink-600'
                  },
                  {
                    type: 'career',
                    icon: '💼',
                    title: '事业指导',
                    desc: '职场发展方向',
                    tag: '3张牌 · 专题',
                    color: 'from-emerald-400 to-emerald-600'
                  }
                ].map((item) => (
                  <div
                    key={item.type}
                    className="group cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2"
                    onClick={() => startReading(item.type as any)}
                  >
                    <div className="relative bg-gradient-to-br from-purple-900/60 to-violet-900/60 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 shadow-2xl overflow-hidden hover:shadow-amber-400/20 transition-all duration-500">
                      {/* 背景光效 */}
                      <div className="absolute inset-0 bg-gradient-to-br from-amber-400/5 to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      
                      {/* 装饰性星星 */}
                      <div className="absolute top-4 right-4 text-amber-300 opacity-60 group-hover:opacity-100 transition-opacity duration-300">✨</div>
                      <div className="absolute bottom-4 left-4 text-amber-300 opacity-40 group-hover:opacity-80 transition-opacity duration-300">🌙</div>
                      
                      <div className="relative z-10">
                        <div className="text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </div>
                        <h3 className="text-xl font-bold mb-3 text-center text-amber-100 group-hover:text-amber-50 transition-colors duration-300">
                          {item.title}
                        </h3>
                        <p className="text-purple-200 text-center mb-6 leading-relaxed text-sm">
                          {item.desc}
                        </p>
                        <div className="text-center">
                          <span className={`inline-block px-4 py-2 bg-gradient-to-r ${item.color} rounded-full text-sm font-semibold text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                            {item.tag}
                          </span>
                        </div>
                      </div>
                      
                      {/* 装饰性边框 */}
                      <div className="absolute inset-0 rounded-3xl border border-amber-400/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {currentStep === 'question' && (
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <div className="text-6xl mb-6">💭</div>
              <h2 className="text-4xl font-bold mb-6 text-amber-300">请输入你的问题</h2>
              <p className="text-purple-200 mb-8">让塔罗牌为你揭示答案</p>
            </div>
            
            <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20">
              <textarea
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                placeholder="请输入你想要了解的问题..."
                className="w-full h-32 bg-purple-800/30 border border-amber-400/30 rounded-2xl p-4 text-white placeholder-purple-300 focus:outline-none focus:border-amber-400/60 transition-colors resize-none"
              />
              
              <div className="text-center mt-8">
                <button
                  onClick={() => setCurrentStep('spread')}
                  disabled={!question.trim()}
                  className="bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 disabled:from-gray-600 disabled:to-gray-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 disabled:scale-100 shadow-lg"
                >
                  开始占卜 ✨
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
