#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版塔罗牌裁剪工具 - 980×1600像素
无需图形界面，命令行操作
"""

import os
import sys
from PIL import Image
import glob

def crop_center(image_path, target_width=980, target_height=1600):
    """
    中心裁剪图片到指定尺寸
    """
    try:
        # 打开图片
        img = Image.open(image_path)
        width, height = img.size
        
        print(f"原始尺寸: {width}×{height}")
        
        # 如果原图太小，先放大
        if width < target_width or height < target_height:
            scale = max(target_width / width, target_height / height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            width, height = new_width, new_height
            print(f"放大后尺寸: {width}×{height}")
        
        # 计算中心裁剪坐标
        left = (width - target_width) // 2
        top = (height - target_height) // 2
        right = left + target_width
        bottom = top + target_height
        
        # 裁剪
        cropped = img.crop((left, top, right, bottom))
        
        return cropped
        
    except Exception as e:
        print(f"裁剪失败: {e}")
        return None

def main():
    print("🎴 塔罗牌简化裁剪工具 - 980×1600")
    print("=" * 50)
    
    # 获取当前目录下的图片文件
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(glob.glob(ext))
        image_files.extend(glob.glob(ext.upper()))
    
    if not image_files:
        print("❌ 当前目录下没有找到图片文件")
        print("请将图片文件放在同一目录下再运行")
        input("按回车键退出...")
        return
    
    print(f"📁 找到 {len(image_files)} 张图片")
    
    # 创建输出目录
    output_dir = "cropped_980x1600"
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理每张图片
    success_count = 0
    
    for i, image_path in enumerate(image_files, 1):
        print(f"\n📸 处理第 {i}/{len(image_files)} 张: {image_path}")
        
        # 裁剪图片
        cropped = crop_center(image_path)
        
        if cropped:
            # 生成输出文件名
            filename = os.path.basename(image_path)
            name, ext = os.path.splitext(filename)
            output_path = os.path.join(output_dir, f"{name}_980x1600{ext}")
            
            # 保存
            cropped.save(output_path, quality=90)
            print(f"✅ 保存成功: {output_path}")
            success_count += 1
        else:
            print(f"❌ 处理失败: {image_path}")
    
    print("\n" + "=" * 50)
    print(f"🎯 处理完成！")
    print(f"✅ 成功: {success_count} 张")
    print(f"❌ 失败: {len(image_files) - success_count} 张")
    print(f"📁 输出目录: {output_dir}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
