// 测试 DeepSeek API 连接
const https = require('https');

const apiKey = 'sk-d3ec8943f3ab4d4abc5c0d16c19665ff';
const baseURL = 'https://api.deepseek.com';

const data = JSON.stringify({
  model: 'deepseek-chat',
  messages: [{ role: 'user', content: '你好' }],
  max_tokens: 10
});

const options = {
  hostname: 'api.deepseek.com',
  port: 443,
  path: '/v1/chat/completions',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
    'Content-Length': data.length
  }
};

console.log('正在测试 DeepSeek API 连接...');

const req = https.request(options, (res) => {
  console.log(`状态码: ${res.statusCode}`);
  console.log(`响应头:`, res.headers);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    console.log('响应内容:', responseData);
    try {
      const parsed = JSON.parse(responseData);
      console.log('解析后的响应:', JSON.stringify(parsed, null, 2));
    } catch (e) {
      console.log('无法解析 JSON 响应');
    }
  });
});

req.on('error', (error) => {
  console.error('请求错误:', error);
});

req.write(data);
req.end();
