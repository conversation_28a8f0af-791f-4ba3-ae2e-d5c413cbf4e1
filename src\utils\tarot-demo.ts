/**
 * 塔罗牌工具演示和测试
 * 展示如何使用各种塔罗牌工具
 */

import { createDrawEngine, quickDraw } from '@/lib/tarot-draw';
import { SpreadManager, TAROT_SPREADS } from '@/lib/tarot-spreads';
import { TarotInterpreter, interpretationHelper } from '@/lib/tarot-interpreter';
import { 
  ReadingType, 
  SpreadType, 
  TarotOrientation, 
  DrawnCard,
  TarotReading 
} from '@/types/tarot';

/**
 * 演示类 - 展示塔罗牌工具的使用
 */
export class TarotDemo {
  
  /**
   * 演示基础抽牌功能
   */
  static demonstrateBasicDraw(): void {
    console.log('=== 塔罗牌抽牌演示 ===\n');

    // 1. 抽取单张牌
    console.log('1. 抽取单张牌：');
    const singleCard = quickDraw.single(true);
    console.log(`   牌名：${singleCard.card.name}`);
    console.log(`   方向：${singleCard.orientation === TarotOrientation.UPRIGHT ? '正位' : '逆位'}`);
    console.log(`   含义：${singleCard.orientation === TarotOrientation.UPRIGHT ? singleCard.card.uprightMeaning : singleCard.card.reversedMeaning}`);
    console.log('');

    // 2. 抽取三张牌
    console.log('2. 抽取三张牌 (过去-现在-未来)：');
    const threeCards = quickDraw.threeCcard(true);
    threeCards.forEach((card, index) => {
      const positions = ['过去', '现在', '未来'];
      console.log(`   ${positions[index]}：${card.card.name} (${card.orientation === TarotOrientation.UPRIGHT ? '正位' : '逆位'})`);
    });
    console.log('');

    // 3. 只抽大阿卡纳
    console.log('3. 只抽大阿卡纳牌：');
    const majorCard = quickDraw.majorOnly(1, true)[0];
    console.log(`   大阿卡纳：${majorCard.card.name}`);
    console.log(`   数字学：${majorCard.card.numerology}`);
    console.log(`   元素：${majorCard.card.element}`);
    console.log('');
  }

  /**
   * 演示高级抽牌功能
   */
  static demonstrateAdvancedDraw(): void {
    console.log('=== 高级抽牌功能演示 ===\n');

    const drawEngine = createDrawEngine('demo_user');

    // 1. 自定义抽牌选项
    console.log('1. 自定义抽牌选项：');
    const customCards = drawEngine.drawMultipleCards(5, {
      allowReversed: false,  // 不允许逆位
      shuffleCount: 7,       // 洗牌7次
      excludeCards: [0, 13]  // 排除愚者和死神
    });

    customCards.forEach((card, index) => {
      console.log(`   位置${index + 1}：${card.card.name} (${card.card.suit})`);
    });
    console.log('');

    // 2. 牌组统计
    console.log('2. 牌组统计：');
    const stats = drawEngine.getDeckStats();
    console.log(`   总牌数：${stats.totalCards}`);
    console.log(`   大阿卡纳：${stats.majorArcana} 张`);
    console.log(`   小阿卡纳：${stats.minorArcana} 张`);
    console.log('');

    // 3. 随机性验证
    console.log('3. 随机性验证测试 (100次抽样)：');
    const randomnessTest = drawEngine.validateRandomness(100);
    console.log(`   随机性良好：${randomnessTest.isRandom ? '是' : '否'}`);
    console.log(`   卡方统计量：${randomnessTest.chiSquare.toFixed(2)}`);
    console.log('');
  }

  /**
   * 演示牌阵功能
   */
  static demonstrateSpreads(): void {
    console.log('=== 牌阵布局演示 ===\n');

    // 1. 显示所有可用牌阵
    console.log('1. 所有可用牌阵：');
    const allSpreads = SpreadManager.getAllSpreads();
    allSpreads.forEach(spread => {
      console.log(`   ${spread.name} (${spread.cardCount}张牌) - ${spread.description}`);
    });
    console.log('');

    // 2. 爱情占卜推荐牌阵
    console.log('2. 爱情占卜推荐牌阵：');
    const loveSpreads = SpreadManager.getSpreadsForReading(ReadingType.LOVE);
    loveSpreads.forEach(spread => {
      console.log(`   ${spread.name} - 适合${spread.suitable.join('、')}占卜`);
    });
    console.log('');

    // 3. 初学者推荐牌阵
    console.log('3. 初学者推荐牌阵：');
    const beginnerSpreads = SpreadManager.getRecommendedSpreads('beginner');
    beginnerSpreads.forEach(spread => {
      console.log(`   ${spread.name} - ${spread.description}`);
    });
    console.log('');

    // 4. 牌阵详细信息
    console.log('4. 凯尔特十字牌阵详细信息：');
    const celticCross = TAROT_SPREADS[SpreadType.CELTIC_CROSS];
    console.log(`   名称：${celticCross.name}`);
    console.log(`   牌数：${celticCross.cardCount}`);
    console.log(`   位置详情：`);
    celticCross.positions.forEach(position => {
      console.log(`     ${position.name} (${position.x}%, ${position.y}%) - ${position.meaning}`);
    });
    console.log('');
  }

  /**
   * 演示完整的占卜流程
   */
  static demonstrateFullReading(): TarotReading {
    console.log('=== 完整占卜流程演示 ===\n');

    // 1. 设置占卜参数
    const question = '我的事业发展前景如何？';
    const readingType = ReadingType.CAREER;
    const spreadType = SpreadType.THREE_CARD;

    console.log(`问题：${question}`);
    console.log(`占卜类型：${readingType}`);
    console.log(`使用牌阵：${spreadType}`);
    console.log('');

    // 2. 抽牌
    console.log('正在抽牌...');
    const drawnCards = quickDraw.threeCcard(true);
    
    console.log('抽到的牌：');
    drawnCards.forEach((card, index) => {
      const positions = ['过去/基础', '现在/状况', '未来/趋势'];
      console.log(`  ${positions[index]}：${card.card.name} (${card.orientation === TarotOrientation.UPRIGHT ? '正位' : '逆位'})`);
    });
    console.log('');

    // 3. 生成解读
    console.log('生成详细解读...');
    const reading = interpretationHelper.detailedInterpret(
      drawnCards,
      spreadType,
      readingType,
      question
    );

    console.log('解读完成！');
    console.log(`解读ID：${reading.id}`);
    console.log(`创建时间：${reading.createdAt.toLocaleString()}`);
    console.log('');

    return reading;
  }

  /**
   * 演示不同类型的解读
   */
  static demonstrateInterpretationTypes(): void {
    console.log('=== 解读类型演示 ===\n');

    // 准备测试数据
    const testCards = quickDraw.multiple(3, true);

    // 1. 简要解读
    console.log('1. 简要解读：');
    const quickInterpretation = interpretationHelper.quickInterpret(
      testCards,
      SpreadType.THREE_CARD,
      ReadingType.GENERAL,
      '今天的整体运势如何？'
    );
    console.log(quickInterpretation.substring(0, 200) + '...');
    console.log('');

    // 2. 详细解读
    console.log('2. 详细解读 (包含数字学和元素分析)：');
    const detailedReading = interpretationHelper.detailedInterpret(
      testCards,
      SpreadType.THREE_CARD,
      ReadingType.LOVE,
      '我的感情关系走向如何？'
    );
    console.log(`解读长度：${detailedReading.interpretation.length} 字符`);
    console.log('包含：牌面解读、关系分析、数字学洞察、元素平衡、建议等内容');
    console.log('');

    // 3. 不同占卜类型的对比
    console.log('3. 不同占卜类型的解读对比：');
    const readingTypes = [ReadingType.LOVE, ReadingType.CAREER, ReadingType.HEALTH];
    
    readingTypes.forEach(type => {
      const reading = interpretationHelper.quickInterpret(
        testCards.slice(0, 1), // 只用一张牌做演示
        SpreadType.SINGLE_CARD,
        type,
        '请给我指引'
      );
      console.log(`   ${type}类型解读长度：${reading.length} 字符`);
    });
    console.log('');
  }

  /**
   * 运行所有演示
   */
  static runAllDemos(): TarotReading {
    console.log('🔮 塔罗牌工具完整演示开始 🔮\n');
    
    this.demonstrateBasicDraw();
    this.demonstrateAdvancedDraw();
    this.demonstrateSpreads();
    this.demonstrateInterpretationTypes();
    
    console.log('🎯 开始完整占卜演示...\n');
    const fullReading = this.demonstrateFullReading();
    
    console.log('✨ 所有演示完成！ ✨');
    
    return fullReading;
  }

  /**
   * 性能测试
   */
  static performanceTest(): void {
    console.log('=== 性能测试 ===\n');

    // 测试抽牌性能
    console.log('1. 抽牌性能测试：');
    const startTime = Date.now();
    
    for (let i = 0; i < 1000; i++) {
      quickDraw.single(true);
    }
    
    const drawTime = Date.now() - startTime;
    console.log(`   1000次单牌抽取耗时：${drawTime}ms`);
    console.log(`   平均每次抽牌：${(drawTime / 1000).toFixed(2)}ms`);
    console.log('');

    // 测试解读生成性能
    console.log('2. 解读生成性能测试：');
    const testCards = quickDraw.threeCcard(true);
    const interpretStartTime = Date.now();
    
    for (let i = 0; i < 100; i++) {
      interpretationHelper.quickInterpret(
        testCards,
        SpreadType.THREE_CARD,
        ReadingType.GENERAL
      );
    }
    
    const interpretTime = Date.now() - interpretStartTime;
    console.log(`   100次解读生成耗时：${interpretTime}ms`);
    console.log(`   平均每次解读：${(interpretTime / 100).toFixed(2)}ms`);
    console.log('');
  }
}

/**
 * 工具验证函数
 */
export const validateTools = (): boolean => {
  console.log('=== 工具验证 ===\n');

  try {
    // 验证抽牌工具
    const card = quickDraw.single(true);
    if (!card || !card.card) {
      console.error('❌ 抽牌工具验证失败');
      return false;
    }
    console.log('✅ 抽牌工具验证通过');

    // 验证牌阵工具
    const spread = SpreadManager.getSpread(SpreadType.THREE_CARD);
    if (!spread || spread.cardCount !== 3) {
      console.error('❌ 牌阵工具验证失败');
      return false;
    }
    console.log('✅ 牌阵工具验证通过');

    // 验证解读工具
    const cards = quickDraw.threeCcard(true);
    const interpretation = interpretationHelper.quickInterpret(
      cards,
      SpreadType.THREE_CARD,
      ReadingType.GENERAL
    );
    if (!interpretation || interpretation.length < 50) {
      console.error('❌ 解读工具验证失败');
      return false;
    }
    console.log('✅ 解读工具验证通过');

    console.log('\n🎉 所有工具验证通过！');
    return true;

  } catch (error) {
    console.error('❌ 工具验证过程中出现错误：', error);
    return false;
  }
};

// 导出快捷使用函数
export const quickDemo = {
  /**
   * 快速体验 - 单张牌占卜
   */
  quickReading: (question: string = '请给我今天的指引') => {
    const card = quickDraw.single(true);
    const interpretation = interpretationHelper.quickInterpret(
      [card],
      SpreadType.SINGLE_CARD,
      ReadingType.GENERAL,
      question
    );
    
    return {
      card: card.card.name,
      orientation: card.orientation === TarotOrientation.UPRIGHT ? '正位' : '逆位',
      interpretation
    };
  },

  /**
   * 三张牌简单占卜
   */
  threeCardReading: (question: string = '我的过去、现在和未来') => {
    const cards = quickDraw.threeCcard(true);
    const interpretation = interpretationHelper.quickInterpret(
      cards,
      SpreadType.THREE_CARD,
      ReadingType.GENERAL,
      question
    );
    
    return {
      cards: cards.map(c => ({
        name: c.card.name,
        orientation: c.orientation === TarotOrientation.UPRIGHT ? '正位' : '逆位'
      })),
      interpretation
    };
  }
};
