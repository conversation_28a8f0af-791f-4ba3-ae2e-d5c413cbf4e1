# 塔罗牌网站项目技术文档

## 项目概述

本项目旨在开发一个功能完整的塔罗牌占卜网站，提供专业的塔罗牌抽牌、解读和用户管理功能。

## 技术架构

### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **动画**: Framer Motion
- **状态管理**: Zustand
- **表单处理**: React Hook Form + Zod
- **HTTP客户端**: Axios
- **UI组件**: Headless UI + 自定义组件

### 后端技术栈
- **API**: Next.js API Routes
- **语言**: TypeScript
- **ORM**: Prisma
- **认证**: NextAuth.js
- **数据验证**: Zod
- **文件上传**: Cloudinary
- **邮件服务**: Resend

### 数据库
- **主数据库**: PostgreSQL (Supabase)
- **缓存**: Redis (Upstash)
- **文件存储**: Cloudinary

### 部署与运维
- **部署平台**: Vercel
- **域名**: 自定义域名
- **CDN**: Vercel Edge Network
- **监控**: Vercel Analytics
- **错误追踪**: Sentry

## 核心功能模块

### 1. 用户系统
- **用户注册/登录**
  - 邮箱注册
  - 社交登录 (Google, GitHub)
  - 邮箱验证
- **用户资料管理**
  - 个人信息编辑
  - 头像上传
  - 占卜偏好设置
- **权限管理**
  - 普通用户
  - VIP用户
  - 管理员

### 2. 塔罗牌核心系统
- **牌组管理**
  - 韦特塔罗牌 (78张)
  - 牌面详细信息
  - 正逆位含义
- **抽牌引擎**
  - 随机抽牌算法
  - 牌阵选择 (单张、三张、十字、凯尔特十字等)
  - 抽牌动画效果
- **解读系统**
  - AI智能解读 (OpenAI API)
  - 预设解读模板
  - 牌面组合解读
  - 时间线解读

### 3. 占卜功能
- **占卜分类**
  - 爱情运势
  - 事业财运
  - 健康运势
  - 综合运势
- **占卜历史**
  - 历史记录查看
  - 收藏重要占卜
  - 分享功能
- **付费功能**
  - 深度解读
  - 专家一对一咨询
  - 高级牌阵

### 4. 内容管理
- **牌面管理**
  - 牌面图片上传
  - 含义编辑
  - 关键词管理
- **文章系统**
  - 塔罗知识文章
  - 学习教程
  - 用户心得分享
- **FAQ系统**

### 5. 支付系统
- **支付集成**
  - 微信支付
  - 支付宝
  - Stripe (国际支付)
- **会员系统**
  - 月度/年度会员
  - 积分系统
  - 优惠券

## 项目结构

```
tarot-website/
├── README.md
├── next.config.js
├── package.json
├── tailwind.config.js
├── tsconfig.json
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── public/
│   ├── images/
│   │   ├── cards/          # 塔罗牌图片
│   │   └── layouts/        # 牌阵布局图
│   └── icons/
├── src/
│   ├── app/                # Next.js App Router
│   │   ├── (auth)/         # 认证相关页面
│   │   ├── (dashboard)/    # 用户仪表板
│   │   ├── (public)/       # 公开页面
│   │   ├── api/            # API路由
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/         # 公共组件
│   │   ├── ui/             # 基础UI组件
│   │   ├── cards/          # 塔罗牌相关组件
│   │   ├── layouts/        # 布局组件
│   │   └── forms/          # 表单组件
│   ├── lib/                # 工具库
│   │   ├── auth.ts         # 认证配置
│   │   ├── database.ts     # 数据库连接
│   │   ├── utils.ts        # 工具函数
│   │   └── validations.ts  # 数据验证
│   ├── hooks/              # 自定义Hooks
│   ├── stores/             # Zustand状态管理
│   ├── types/              # TypeScript类型定义
│   └── constants/          # 常量定义
└── docs/                   # 项目文档
```

## 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
- id: UUID (主键)
- email: VARCHAR (唯一)
- username: VARCHAR (唯一)
- password_hash: VARCHAR
- avatar_url: VARCHAR
- role: ENUM (user, vip, admin)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### 塔罗牌表 (tarot_cards)
```sql
- id: INT (主键)
- name: VARCHAR (牌名)
- name_en: VARCHAR (英文名)
- suit: VARCHAR (花色)
- number: INT (牌号)
- image_url: VARCHAR (图片链接)
- upright_meaning: TEXT (正位含义)
- reversed_meaning: TEXT (逆位含义)
- keywords: JSON (关键词)
```

#### 占卜记录表 (readings)
```sql
- id: UUID (主键)
- user_id: UUID (外键)
- layout_type: VARCHAR (牌阵类型)
- question: TEXT (问题)
- cards_drawn: JSON (抽到的牌)
- interpretation: TEXT (解读结果)
- created_at: TIMESTAMP
- is_public: BOOLEAN
- is_favorite: BOOLEAN
```

#### 支付记录表 (payments)
```sql
- id: UUID (主键)
- user_id: UUID (外键)
- amount: DECIMAL (金额)
- currency: VARCHAR (货币)
- payment_method: VARCHAR (支付方式)
- status: ENUM (pending, completed, failed)
- created_at: TIMESTAMP
```

## 开发计划

### 第一阶段 (2-3周)
- [x] 项目初始化和基础架构搭建
- [ ] 用户认证系统开发
- [ ] 基础UI组件库建设
- [ ] 数据库设计和模型创建

### 第二阶段 (3-4周)
- [ ] 塔罗牌数据导入和管理
- [ ] 核心抽牌功能开发
- [ ] 基础牌阵实现
- [ ] 前端交互和动画

### 第三阶段 (2-3周)
- [ ] AI解读功能集成
- [ ] 历史记录系统
- [ ] 用户个人中心
- [ ] 响应式设计优化

### 第四阶段 (2-3周)
- [ ] 支付系统集成
- [ ] 会员功能开发
- [ ] 管理后台开发
- [ ] 性能优化

### 第五阶段 (1-2周)
- [ ] 测试和bug修复
- [ ] 安全性检查
- [ ] 部署和上线
- [ ] 文档完善

## 技术难点和解决方案

### 1. 随机抽牌算法
**难点**: 确保真正的随机性和避免重复
**解决方案**: 使用加密级随机数生成器，结合时间戳和用户ID作为种子

### 2. 牌面动画效果
**难点**: 流畅的翻牌和布局动画
**解决方案**: 使用Framer Motion，预加载资源，CSS3D变换

### 3. AI解读准确性
**难点**: 生成有意义的解读内容
**解决方案**: 精心设计Prompt，结合牌面含义和用户问题context

### 4. 性能优化
**难点**: 大量图片资源加载
**解决方案**: 图片懒加载，CDN分发，WebP格式，图片压缩

## 安全考虑

- **数据加密**: 敏感数据加密存储
- **API安全**: Rate limiting，CORS配置
- **认证安全**: JWT token，密码强度验证
- **支付安全**: PCI DSS合规，第三方支付接口
- **数据验证**: 前后端双重验证，防止SQL注入

## 监控和分析

- **错误监控**: Sentry错误追踪
- **性能监控**: Core Web Vitals监控
- **用户行为**: Google Analytics 4
- **业务指标**: 自定义数据看板

## 后续扩展计划

- **移动端应用**: React Native开发
- **多语言支持**: i18n国际化
- **社交功能**: 用户社区，分享功能
- **专家系统**: 专业塔罗师入驻
- **直播功能**: 在线占卜直播

---

**项目开始时间**: 即日开始  
**预计完成时间**: 3-4个月  
**团队规模**: 1-2人  
**预算估算**: 根据功能需求和云服务使用量确定
