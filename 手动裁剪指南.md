# 🖼️ 手动裁剪塔罗牌指南 - 980×1600

## 🎯 由于Python环境问题，这里提供手动裁剪方案

### 方案1：使用在线工具（最简单）⭐⭐⭐⭐⭐

#### **推荐网站：Photopea（免费的在线Photoshop）**

**网址**: https://www.photopea.com/

**步骤**：
1. 打开 https://www.photopea.com/
2. 点击"打开文件"，上传您的塔罗牌图片
3. 选择"裁剪工具"（快捷键C）
4. 在顶部工具栏设置：
   - 宽度：980px
   - 高度：1600px
   - 分辨率：保持不变
5. 拖拽裁剪框到理想位置
6. 按回车确认裁剪
7. 文件 → 导出为 → JPG
8. 下载文件

#### **批量在线工具：ILoveIMG**

**网址**: https://www.iloveimg.com/crop-image

**步骤**：
1. 访问 https://www.iloveimg.com/crop-image
2. 上传图片（支持批量）
3. 对每张图片：
   - 点击"裁剪"
   - 设置自定义尺寸：980×1600
   - 调整位置
   - 确认
4. 下载处理后的图片

### 方案2：使用免费软件

#### **GIMP（推荐，功能强大）**

**下载**: https://www.gimp.org/downloads/

**操作步骤**：
1. 下载安装GIMP
2. 打开图片：文件 → 打开
3. 选择裁剪工具（快捷键Shift+C）
4. 在工具选项中：
   - 选择"固定"→"尺寸"
   - 设置：980×1600
5. 拖拽裁剪框到理想位置
6. 按回车确认
7. 文件 → 导出为 → JPG

#### **Paint.NET（Windows推荐）**

**下载**: https://www.getpaint.net/download.html

**操作步骤**：
1. 下载安装Paint.NET
2. 打开图片
3. 图像 → 裁剪到选择
4. 选择矩形选择工具
5. 在状态栏输入选择框尺寸：980×1600
6. 调整位置后裁剪

### 方案3：使用手机APP

#### **Snapseed（免费，功能强大）**

**步骤**：
1. 下载Snapseed APP
2. 打开图片
3. 工具 → 裁剪
4. 选择"自由"模式
5. 手动调整到合适比例
6. 导出

### 🎯 批量处理建议

#### 对于大量图片：

**使用Photopea批量处理**：
1. 创建"动作"录制裁剪过程
2. 批量应用到所有图片

**使用ILoveIMG**：
1. 一次上传多张图片
2. 逐个设置裁剪
3. 批量下载

### ⚙️ 裁剪参数

```
目标尺寸：980×1600像素
长宽比：98:160 (约0.6125)
保持质量：90%以上
格式：JPG或PNG
```

### 📐 裁剪技巧

1. **保持塔罗牌居中**：确保牌面主体在裁剪框中心
2. **保留重要元素**：不要裁掉关键图案
3. **统一标准**：所有卡牌使用相同的裁剪逻辑
4. **质量优先**：宁可稍微大一点也不要压缩过度

### 📋 批量命名建议

裁剪后统一命名格式：
```
大阿卡纳：00-fool_980x1600.jpg
小阿卡纳：ace-cups_980x1600.jpg
```

### 🔍 质量检查

处理完成后检查：
- ✅ 尺寸确实是980×1600
- ✅ 图片清晰，没有模糊
- ✅ 裁剪位置合适
- ✅ 文件大小合理（通常50-200KB）

---

## 🎉 推荐流程

**对于少量图片（1-10张）**：
→ 使用Photopea在线处理

**对于中等数量（10-30张）**：
→ 下载GIMP，设置固定裁剪尺寸

**对于大量图片（30张以上）**：
→ 使用ILoveIMG批量处理

**现在您可以开始手动裁剪您的塔罗牌图片了！** 🎴✨
