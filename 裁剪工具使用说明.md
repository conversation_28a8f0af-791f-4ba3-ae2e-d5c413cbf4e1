# 🖼️ 塔罗牌裁剪工具使用说明

## 📦 安装依赖

```bash
pip install Pillow tkinter
```

> **注意**: tkinter通常随Python自带，如果提示找不到，请安装：`pip install tk`

## 🚀 运行工具

```bash
python tarot_crop_tool.py
```

## 🎯 功能特点

### ✨ 主要功能
- **精确裁剪**: 固定980×1600像素尺寸
- **可视化操作**: 红色裁剪框，所见即所得
- **手动调整**: 拖拽裁剪框到理想位置
- **批量处理**: 支持单张精调和批量中心裁剪
- **自动保存**: 智能命名和文件夹管理

### 🔧 操作界面
- **选择文件夹**: 批量加载所有图片
- **导航控制**: 上一张/下一张浏览
- **裁剪框控制**: 重置/手动调整位置
- **保存选项**: 单张保存/批量处理

## 📋 使用步骤

### 步骤1: 准备图片
1. 将所有需要裁剪的塔罗牌图片放在一个文件夹中
2. 支持格式：JPG、PNG、BMP、TIFF

### 步骤2: 启动工具
```bash
python tarot_crop_tool.py
```

### 步骤3: 选择文件夹
1. 点击"选择图片文件夹"按钮
2. 选择包含塔罗牌图片的文件夹
3. 工具会自动加载第一张图片

### 步骤4: 调整裁剪位置
1. **自动居中**: 工具会自动创建居中的裁剪框
2. **手动调整**: 拖拽红色裁剪框到理想位置
3. **重置位置**: 点击"重置裁剪框"回到中心位置

### 步骤5: 保存裁剪结果
#### 方式A: 单张精确裁剪
1. 调整裁剪框到满意位置
2. 点击"裁剪并保存"
3. 自动跳转到下一张图片

#### 方式B: 批量处理
1. 点击"批量处理"
2. 确认批量中心裁剪
3. 等待处理完成

## 📁 输出文件

### 单张保存
- **位置**: `原文件夹/cropped_980x1600/`
- **命名**: `原文件名_cropped.jpg`

### 批量保存
- **位置**: `原文件夹/batch_cropped_980x1600/`
- **命名**: `原文件名_cropped.jpg`

## 🎮 操作技巧

### 裁剪框控制
- **拖拽移动**: 在裁剪框内点击拖拽
- **精确定位**: 慢慢移动到理想位置
- **边界限制**: 裁剪框不会超出图片边界

### 快捷操作
- **快速浏览**: 使用"上一张"/"下一张"按钮
- **批量模式**: 对于不需要精确调整的图片使用批量处理
- **质量保证**: 单张模式可以确保每张图片的最佳裁剪位置

## ⚠️ 注意事项

### 图片要求
- **最小尺寸**: 原图应大于980×1600像素
- **如果原图较小**: 工具会自动放大后裁剪（可能影响清晰度）

### 质量建议
- **优先单张调整**: 对重要的大阿卡纳使用手动调整
- **批量处理辅助**: 对相似的小阿卡纳可以使用批量模式
- **备份原图**: 建议保留原始图片

## 🔧 自定义修改

如果您需要调整目标尺寸，修改代码中的这两行：

```python
self.target_width = 980   # 修改为您需要的宽度
self.target_height = 1600 # 修改为您需要的高度
```

## 🚨 常见问题

### Q: 裁剪框太大，超出图片边界
A: 原图尺寸小于980×1600，建议先用其他工具放大图片

### Q: 拖拽不起作用
A: 确保在红色裁剪框内点击并拖拽

### Q: 批量处理结果不理想
A: 批量模式使用中心裁剪，对于需要精确位置的图片请使用单张模式

### Q: 程序运行报错
A: 检查是否安装了PIL库：`pip install Pillow`

## 🎯 推荐工作流程

1. **快速预览**: 先浏览所有图片，了解整体情况
2. **重点调整**: 对22张大阿卡纳进行手动精确裁剪
3. **批量处理**: 对56张小阿卡纳使用批量中心裁剪
4. **质量检查**: 检查输出结果，必要时重新调整

---

**🎴 现在您可以精确控制每张塔罗牌的裁剪位置，确保最佳的视觉效果！**
