<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎴 塔罗牌裁剪工具 - 1030×1795</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .main-content {
            padding: 30px;
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .upload-area.active {
            border-color: #ee5a24;
            background: #fff5f5;
        }

        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .workspace {
            display: none;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-top: 30px;
        }

        .canvas-area {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: #f9f9f9;
            min-height: 500px;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        #imageCanvas {
            display: block;
            cursor: crosshair;
        }

        .crop-overlay {
            position: absolute;
            border: 2px solid #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            cursor: move;
            display: none;
        }

        .crop-overlay::before {
            content: '1030×1795';
            position: absolute;
            top: -25px;
            left: 0;
            background: #ff6b6b;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }

        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .info-display {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            color: #333;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ddd;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 768px) {
            .workspace {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 15px;
            }
        }

        .batch-list {
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }

        .batch-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }

        .batch-item:last-child {
            border-bottom: none;
        }

        .batch-status {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            color: white;
        }

        .batch-status.pending {
            background: #6c757d;
        }

        .batch-status.processing {
            background: #007bff;
        }

        .batch-status.completed {
            background: #28a745;
        }

        .batch-status.error {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎴 塔罗牌裁剪工具</h1>
            <p>专业裁剪到 1030×1795 像素 | 拖拽调整位置 | 批量处理</p>
        </div>

        <div class="main-content">
            <!-- 上传区域 -->
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">拖拽图片到这里，或点击选择文件</div>
                <div style="margin-top: 10px; color: #999; font-size: 0.9em;">
                    支持 JPG、PNG、WebP 格式 | 支持多文件批量上传
                </div>
                <input type="file" id="fileInput" class="file-input" multiple accept="image/*">
                <button class="btn" onclick="selectFiles()">
                    选择图片文件
                </button>
            </div>

            <!-- 工作区域 -->
            <div class="workspace" id="workspace">
                <div class="canvas-area">
                    <div class="canvas-container">
                        <canvas id="imageCanvas"></canvas>
                        <div class="crop-overlay" id="cropOverlay"></div>
                    </div>
                </div>

                <div class="controls">
                    <div class="control-group">
                        <h3>📊 图片信息</h3>
                        <div class="info-display">
                            <div class="info-item">
                                <span class="info-label">文件名:</span>
                                <span class="info-value" id="fileName">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">原始尺寸:</span>
                                <span class="info-value" id="originalSize">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">裁剪位置:</span>
                                <span class="info-value" id="cropPosition">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">目标尺寸:</span>
                                <span class="info-value">1030×1795</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>🎯 裁剪控制</h3>
                        <button class="btn" onclick="resetCropArea()">🔄 重置到中心</button>
                        <button class="btn" onclick="smartCrop()">🤖 智能裁剪</button>
                        <button class="btn" onclick="cropAndSave()">✂️ 裁剪并保存</button>
                    </div>

                    <div class="control-group">
                        <h3>📂 批量处理</h3>
                        <button class="btn" onclick="startManualBatch()" id="manualBatchBtn" disabled>
                            🎯 批量手动调整
                        </button>
                        <button class="btn" onclick="batchCropAll()" id="batchBtn" disabled>
                            🚀 批量自动处理
                        </button>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div style="text-align: center; font-size: 0.9em; color: #666;" id="progressText">
                            准备就绪
                        </div>
                    </div>

                    <div class="control-group" id="navigationGroup" style="display: none;">
                        <h3>🎮 图片导航</h3>
                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                            <button class="btn" onclick="prevImage()" id="prevBtn" disabled>⬅️ 上一张</button>
                            <button class="btn" onclick="nextImage()" id="nextBtn" disabled>➡️ 下一张</button>
                        </div>
                        <button class="btn" onclick="saveAndNext()" id="saveNextBtn" style="background: linear-gradient(45deg, #28a745, #20c997); width: 100%;">
                            ✅ 保存并下一张
                        </button>
                        <div style="text-align: center; margin-top: 10px; font-size: 0.9em; color: #666;" id="imageCounter">
                            -
                        </div>
                        <div style="background: #f0f8ff; padding: 8px; border-radius: 5px; margin-top: 10px; font-size: 0.8em; color: #555;">
                            <strong>⌨️ 快捷键：</strong><br>
                            ← → 切换图片 | Enter/空格 保存下一张 | R 重置裁剪框
                        </div>
                    </div>

                    <div class="control-group" id="batchListGroup" style="display: none;">
                        <h3>📋 处理列表</h3>
                        <div class="batch-list" id="batchList"></div>
                    </div>
                </div>
            </div>

            <div id="statusMessage"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentImage = null;
        let currentFile = null;
        let canvas = null;
        let ctx = null;
        let cropOverlay = null;
        let imageFiles = [];
        let currentFileIndex = 0;
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let cropArea = { x: 0, y: 0, width: 1030, height: 1795 };
        let isManualBatchMode = false;
        let manualBatchResults = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            canvas = document.getElementById('imageCanvas');
            ctx = canvas.getContext('2d');
            cropOverlay = document.getElementById('cropOverlay');
            
            setupEventListeners();
            showStatus('准备就绪 - 请选择图片文件', 'info');
        }

        function selectFiles() {
            const fileInput = document.getElementById('fileInput');
            console.log('按钮点击，准备选择文件');
            fileInput.value = ''; // 清空input确保可以重复选择
            fileInput.click();
        }

        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 拖拽上传
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            uploadArea.addEventListener('click', (e) => {
                // 如果点击的是按钮或input，不处理
                if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT') {
                    return;
                }
                
                // 在点击时清空input，确保每次都能正常选择
                fileInput.value = '';
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 裁剪框拖拽
            cropOverlay.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // Canvas点击
            canvas.addEventListener('click', handleCanvasClick);
            
            // 键盘快捷键支持
            document.addEventListener('keydown', handleKeyPress);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files).filter(file => 
                file.type.startsWith('image/')
            );
            
            if (files.length > 0) {
                loadImageFiles(files);
            }
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            console.log('文件选择事件触发，选择了', files.length, '个文件');
            
            if (files.length > 0) {
                try {
                    loadImageFiles(files);
                    console.log('文件加载成功');
                } catch (error) {
                    console.error('文件加载失败:', error);
                    showStatus('文件加载失败，请重试', 'error');
                }
            } else {
                console.log('没有选择文件');
            }
            
            // 清空input，避免重复选择同一文件无响应
            setTimeout(() => {
                e.target.value = '';
            }, 100);
        }

        function loadImageFiles(files) {
            imageFiles = files;
            currentFileIndex = 0;
            isManualBatchMode = false;
            manualBatchResults = [];
            
            if (files.length === 1) {
                loadSingleImage(files[0]);
                // 隐藏批量导航
                document.getElementById('navigationGroup').style.display = 'none';
                document.getElementById('batchListGroup').style.display = 'none';
            } else {
                loadBatchImages(files);
                // 启用批量按钮
                document.getElementById('manualBatchBtn').disabled = false;
            }
            
            document.getElementById('workspace').style.display = 'grid';
            document.getElementById('batchBtn').disabled = false;
        }

        function loadSingleImage(file) {
            currentFile = file;
            const reader = new FileReader();
            
            reader.onload = function(e) {
                currentImage = new Image();
                currentImage.onload = function() {
                    displayImage();
                    updateImageInfo();
                    resetCropArea();
                    showStatus(`已加载: ${file.name}`, 'success');
                };
                currentImage.src = e.target.result;
            };
            
            reader.readAsDataURL(file);
        }

        function loadBatchImages(files) {
            // 加载第一张图片进行预览
            loadSingleImage(files[0]);
            
            // 显示批量列表
            const batchListGroup = document.getElementById('batchListGroup');
            const batchList = document.getElementById('batchList');
            
            batchListGroup.style.display = 'block';
            batchList.innerHTML = '';
            
            files.forEach((file, index) => {
                const item = document.createElement('div');
                item.className = 'batch-item';
                item.innerHTML = `
                    <span>${file.name}</span>
                    <span class="batch-status pending" id="status-${index}">等待</span>
                `;
                batchList.appendChild(item);
            });
            
            showStatus(`已加载 ${files.length} 张图片，准备批量处理`, 'info');
        }

        function displayImage() {
            if (!currentImage) return;
            
            const maxWidth = 800;
            const maxHeight = 600;
            
            let displayWidth = currentImage.width;
            let displayHeight = currentImage.height;
            
            // 计算显示尺寸
            if (displayWidth > maxWidth || displayHeight > maxHeight) {
                const scale = Math.min(maxWidth / displayWidth, maxHeight / displayHeight);
                displayWidth *= scale;
                displayHeight *= scale;
            }
            
            canvas.width = displayWidth;
            canvas.height = displayHeight;
            
            ctx.clearRect(0, 0, displayWidth, displayHeight);
            ctx.drawImage(currentImage, 0, 0, displayWidth, displayHeight);
            
            // 调整canvas容器大小
            const canvasContainer = canvas.parentElement;
            canvasContainer.style.width = displayWidth + 'px';
            canvasContainer.style.height = displayHeight + 'px';
        }

        function updateImageInfo() {
            if (!currentFile || !currentImage) return;
            
            document.getElementById('fileName').textContent = currentFile.name;
            document.getElementById('originalSize').textContent = 
                `${currentImage.width}×${currentImage.height}`;
        }

        function resetCropArea() {
            if (!currentImage) return;
            
            const scale = Math.min(canvas.width / currentImage.width, canvas.height / currentImage.height);
            
            const cropDisplayWidth = 1030 * scale;
            const cropDisplayHeight = 1795 * scale;
            
            // 确保不超出画布边界
            const maxWidth = Math.min(cropDisplayWidth, canvas.width);
            const maxHeight = Math.min(cropDisplayHeight, canvas.height);
            
            const x = (canvas.width - maxWidth) / 2;
            const y = (canvas.height - maxHeight) / 2;
            
            setCropOverlay(x, y, maxWidth, maxHeight);
            updateCropPosition();
        }

        function setCropOverlay(x, y, width, height) {
            const canvasRect = canvas.getBoundingClientRect();
            const containerRect = canvas.parentElement.getBoundingClientRect();
            
            cropOverlay.style.display = 'block';
            cropOverlay.style.left = (x + canvasRect.left - containerRect.left) + 'px';
            cropOverlay.style.top = (y + canvasRect.top - containerRect.top) + 'px';
            cropOverlay.style.width = width + 'px';
            cropOverlay.style.height = height + 'px';
            
            // 更新全局裁剪区域
            const scale = currentImage.width / canvas.width;
            cropArea.x = x * scale;
            cropArea.y = y * scale;
            cropArea.width = 1030;
            cropArea.height = 1795;
        }

        function updateCropPosition() {
            if (!currentImage) return;
            
            const x = Math.round(cropArea.x);
            const y = Math.round(cropArea.y);
            
            document.getElementById('cropPosition').textContent = `${x}, ${y}`;
        }

        function handleCanvasClick(e) {
            if (!currentImage) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const scale = Math.min(canvas.width / currentImage.width, canvas.height / currentImage.height);
            const cropDisplayWidth = Math.min(1030 * scale, canvas.width);
            const cropDisplayHeight = Math.min(1795 * scale, canvas.height);
            
            // 将点击位置作为裁剪框中心
            const newX = Math.max(0, Math.min(x - cropDisplayWidth/2, canvas.width - cropDisplayWidth));
            const newY = Math.max(0, Math.min(y - cropDisplayHeight/2, canvas.height - cropDisplayHeight));
            
            setCropOverlay(newX, newY, cropDisplayWidth, cropDisplayHeight);
            updateCropPosition();
        }

        function startDrag(e) {
            isDragging = true;
            dragStart.x = e.clientX;
            dragStart.y = e.clientY;
            e.preventDefault();
        }

        function drag(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - dragStart.x;
            const deltaY = e.clientY - dragStart.y;
            
            const currentLeft = parseInt(cropOverlay.style.left);
            const currentTop = parseInt(cropOverlay.style.top);
            const width = parseInt(cropOverlay.style.width);
            const height = parseInt(cropOverlay.style.height);
            
            const canvasRect = canvas.getBoundingClientRect();
            const containerRect = canvas.parentElement.getBoundingClientRect();
            
            const minX = canvasRect.left - containerRect.left;
            const minY = canvasRect.top - containerRect.top;
            const maxX = minX + canvas.width - width;
            const maxY = minY + canvas.height - height;
            
            const newLeft = Math.max(minX, Math.min(currentLeft + deltaX, maxX));
            const newTop = Math.max(minY, Math.min(currentTop + deltaY, maxY));
            
            cropOverlay.style.left = newLeft + 'px';
            cropOverlay.style.top = newTop + 'px';
            
            // 更新全局裁剪区域
            const canvasX = newLeft - (canvasRect.left - containerRect.left);
            const canvasY = newTop - (canvasRect.top - containerRect.top);
            const scale = currentImage.width / canvas.width;
            
            cropArea.x = canvasX * scale;
            cropArea.y = canvasY * scale;
            
            updateCropPosition();
            
            dragStart.x = e.clientX;
            dragStart.y = e.clientY;
        }

        function endDrag() {
            isDragging = false;
        }

        function cropAndSave() {
            if (!currentImage) {
                showStatus('请先加载图片', 'error');
                return;
            }
            
            // 创建裁剪画布
            const cropCanvas = document.createElement('canvas');
            cropCanvas.width = 1030;
            cropCanvas.height = 1795;
            const cropCtx = cropCanvas.getContext('2d');
            
            // 裁剪图片
            cropCtx.drawImage(
                currentImage,
                cropArea.x, cropArea.y, 1030, 1795,
                0, 0, 1030, 1795
            );
            
            // 转换为blob并下载，使用data URL避免安全警告
            cropCanvas.toBlob(function(blob) {
                // 使用FileReader转换为data URL
                const reader = new FileReader();
                reader.onload = function(e) {
                    const a = document.createElement('a');
                    a.href = e.target.result;
                    a.download = generateFileName(currentFile.name);
                    
                    // 添加到页面并触发点击
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    
                    showStatus('裁剪完成并已下载', 'success');
                };
                reader.readAsDataURL(blob);
            }, 'image/jpeg', 0.9);
        }

        // 全局变量存储处理结果
        let processedImages = [];

        function batchCropAll() {
            if (imageFiles.length === 0) return;
            
            processedImages = []; // 重置结果数组
            showStatus('开始批量处理...', 'info');
            updateProgress(0);
            
            processNextImage(0);
        }

        function processNextImage(index) {
            if (index >= imageFiles.length) {
                showStatus(`批量处理完成！请点击下载按钮获取文件`, 'success');
                updateProgress(100);
                showDownloadButtons();
                return;
            }
            
            const file = imageFiles[index];
            const statusElement = document.getElementById(`status-${index}`);
            
            if (statusElement) {
                statusElement.textContent = '处理中';
                statusElement.className = 'batch-status processing';
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    try {
                        // 自动中心裁剪
                        const cropCanvas = document.createElement('canvas');
                        cropCanvas.width = 1030;
                        cropCanvas.height = 1795;
                        const cropCtx = cropCanvas.getContext('2d');
                        
                        // 计算中心裁剪位置
                        const centerX = Math.max(0, (img.width - 1030) / 2);
                        const centerY = Math.max(0, (img.height - 1795) / 2);
                        
                        // 如果原图小于目标尺寸，先缩放
                        if (img.width < 1030 || img.height < 1795) {
                            const scale = Math.max(1030 / img.width, 1795 / img.height);
                            const scaledWidth = img.width * scale;
                            const scaledHeight = img.height * scale;
                            
                            const tempCanvas = document.createElement('canvas');
                            tempCanvas.width = scaledWidth;
                            tempCanvas.height = scaledHeight;
                            const tempCtx = tempCanvas.getContext('2d');
                            tempCtx.drawImage(img, 0, 0, scaledWidth, scaledHeight);
                            
                            const newCenterX = (scaledWidth - 1030) / 2;
                            const newCenterY = (scaledHeight - 1795) / 2;
                            
                            cropCtx.drawImage(
                                tempCanvas,
                                newCenterX, newCenterY, 1030, 1795,
                                0, 0, 1030, 1795
                            );
                        } else {
                            cropCtx.drawImage(
                                img,
                                centerX, centerY, 1030, 1795,
                                0, 0, 1030, 1795
                            );
                        }
                        
                        cropCanvas.toBlob(function(blob) {
                            // 存储到结果数组而不是立即下载
                            processedImages.push({
                                blob: blob,
                                filename: generateFileName(file.name),
                                originalName: file.name
                            });
                            
                            if (statusElement) {
                                statusElement.textContent = '完成';
                                statusElement.className = 'batch-status completed';
                            }
                            
                            const progress = ((index + 1) / imageFiles.length) * 100;
                            updateProgress(progress);
                            
                            // 处理下一张
                            setTimeout(() => processNextImage(index + 1), 50);
                            
                        }, 'image/jpeg', 0.9);
                        
                    } catch (error) {
                        console.error('处理图片失败:', error);
                        if (statusElement) {
                            statusElement.textContent = '失败';
                            statusElement.className = 'batch-status error';
                        }
                        processNextImage(index + 1);
                    }
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function showDownloadButtons() {
            // 添加下载按钮区域
            const controlGroup = document.querySelector('.control-group:last-child');
            
            // 移除旧的下载按钮（如果存在）
            const existingDownloadArea = document.getElementById('downloadArea');
            if (existingDownloadArea) {
                existingDownloadArea.remove();
            }
            
            const downloadArea = document.createElement('div');
            downloadArea.id = 'downloadArea';
            downloadArea.innerHTML = `
                <h3>📥 下载选项</h3>
                <button class="btn" onclick="downloadAllAsZip()">📦 打包下载 (ZIP)</button>
                <button class="btn" onclick="downloadOneByOne()">📁 逐个下载</button>
                <div style="margin-top: 10px;">
                    <small style="color: #666;">
                        推荐使用"打包下载"，或点击"逐个下载"然后允许浏览器多重下载
                    </small>
                </div>
            `;
            
            controlGroup.appendChild(downloadArea);
        }

        function updateProgress(percent) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = percent + '%';
            
            if (percent === 0) {
                progressText.textContent = '准备开始...';
            } else if (percent === 100) {
                progressText.textContent = '处理完成！';
            } else {
                progressText.textContent = `处理中... ${Math.round(percent)}%`;
            }
        }

        function generateFileName(originalName) {
            const nameWithoutExt = originalName.replace(/\.[^/.]+$/, "");
            const ext = originalName.split('.').pop();
            return `${nameWithoutExt}_1030x1795.${ext}`;
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 智能裁剪功能
        function smartCrop() {
            if (!currentImage) {
                showStatus('请先加载图片', 'error');
                return;
            }
            
            const img = currentImage;
            const imgWidth = img.width;
            const imgHeight = img.height;
            const targetRatio = 1030 / 1795; // 目标比例
            const currentRatio = imgWidth / imgHeight;
            
            let cropX, cropY, cropWidth, cropHeight;
            
            if (currentRatio > targetRatio) {
                // 图片太宽，按高度裁剪
                cropHeight = imgHeight;
                cropWidth = imgHeight * targetRatio;
                cropX = (imgWidth - cropWidth) / 2;
                cropY = 0;
            } else {
                // 图片太高，按宽度裁剪
                cropWidth = imgWidth;
                cropHeight = imgWidth / targetRatio;
                cropX = 0;
                cropY = (imgHeight - cropHeight) / 2;
            }
            
            // 更新全局裁剪区域
            cropArea.x = cropX;
            cropArea.y = cropY;
            cropArea.width = 1030;
            cropArea.height = 1795;
            
            // 更新显示
            const scale = Math.min(canvas.width / imgWidth, canvas.height / imgHeight);
            const displayX = cropX * scale;
            const displayY = cropY * scale;
            const displayWidth = cropWidth * scale;
            const displayHeight = cropHeight * scale;
            
            // 调整裁剪框到画布坐标系
            const canvasRect = canvas.getBoundingClientRect();
            const containerRect = canvas.parentElement.getBoundingClientRect();
            const offsetX = canvasRect.left - containerRect.left + (canvas.width - imgWidth * scale) / 2;
            const offsetY = canvasRect.top - containerRect.top + (canvas.height - imgHeight * scale) / 2;
            
            setCropOverlay(displayX + offsetX, displayY + offsetY, displayWidth, displayHeight);
            updateCropPosition();
            
            showStatus('智能裁剪完成！已自动调整到最佳比例', 'success');
        }

        // 键盘快捷键处理
        function handleKeyPress(e) {
            // 只在批量手动调整模式下启用快捷键
            if (!isManualBatchMode) return;
            
            // 防止在输入框中触发快捷键
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    prevImage();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    nextImage();
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    saveAndNext();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    resetCropArea();
                    break;
            }
        }

        // 批量手动调整功能
        function startManualBatch() {
            if (imageFiles.length === 0) {
                showStatus('请先选择图片文件', 'error');
                return;
            }
            
            isManualBatchMode = true;
            manualBatchResults = [];
            currentFileIndex = 0;
            
            // 显示导航控制
            document.getElementById('navigationGroup').style.display = 'block';
            document.getElementById('batchListGroup').style.display = 'block';
            
            // 更新批量列表状态
            updateBatchListForManual();
            
            // 加载第一张图片
            loadCurrentImageInBatch();
            
            showStatus('批量手动调整模式已开启！调整位置后点击"保存并下一张"', 'info');
        }
        
        function updateBatchListForManual() {
            const batchList = document.getElementById('batchList');
            batchList.innerHTML = '';
            
            imageFiles.forEach((file, index) => {
                const item = document.createElement('div');
                item.className = 'batch-item';
                item.innerHTML = `
                    <span>${file.name}</span>
                    <span class="batch-status pending" id="manual-status-${index}">等待</span>
                `;
                if (index === currentFileIndex) {
                    item.style.background = '#e3f2fd';
                }
                batchList.appendChild(item);
            });
        }
        
        function loadCurrentImageInBatch() {
            if (currentFileIndex >= imageFiles.length) {
                finishManualBatch();
                return;
            }
            
            loadSingleImage(imageFiles[currentFileIndex]);
            updateImageCounter();
            updateNavigationButtons();
            updateBatchListHighlight();
            
            // 更新当前项状态
            const statusElement = document.getElementById(`manual-status-${currentFileIndex}`);
            if (statusElement) {
                statusElement.textContent = '调整中';
                statusElement.className = 'batch-status processing';
            }
        }
        
        function updateImageCounter() {
            const counter = document.getElementById('imageCounter');
            counter.textContent = `第 ${currentFileIndex + 1} 张 / 共 ${imageFiles.length} 张`;
        }
        
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const saveNextBtn = document.getElementById('saveNextBtn');
            
            prevBtn.disabled = currentFileIndex === 0;
            nextBtn.disabled = currentFileIndex >= imageFiles.length - 1;
            
            if (currentFileIndex >= imageFiles.length - 1) {
                saveNextBtn.textContent = '✅ 保存并完成';
            } else {
                saveNextBtn.textContent = '✅ 保存并下一张';
            }
        }
        
        function updateBatchListHighlight() {
            const batchList = document.getElementById('batchList');
            const items = batchList.children;
            
            for (let i = 0; i < items.length; i++) {
                if (i === currentFileIndex) {
                    items[i].style.background = '#e3f2fd';
                } else {
                    items[i].style.background = '';
                }
            }
        }
        
        function prevImage() {
            if (currentFileIndex > 0) {
                currentFileIndex--;
                loadCurrentImageInBatch();
            }
        }
        
        function nextImage() {
            if (currentFileIndex < imageFiles.length - 1) {
                currentFileIndex++;
                loadCurrentImageInBatch();
            }
        }
        
        function saveAndNext() {
            if (!currentImage) {
                showStatus('请先加载图片', 'error');
                return;
            }
            
            // 保存当前图片
            const cropCanvas = document.createElement('canvas');
            cropCanvas.width = 1030;
            cropCanvas.height = 1795;
            const cropCtx = cropCanvas.getContext('2d');
            
            cropCtx.drawImage(
                currentImage,
                cropArea.x, cropArea.y, 1030, 1795,
                0, 0, 1030, 1795
            );
            
            cropCanvas.toBlob(function(blob) {
                // 保存到结果数组
                manualBatchResults.push({
                    blob: blob,
                    filename: generateFileName(imageFiles[currentFileIndex].name),
                    originalName: imageFiles[currentFileIndex].name
                });
                
                // 更新状态
                const statusElement = document.getElementById(`manual-status-${currentFileIndex}`);
                if (statusElement) {
                    statusElement.textContent = '已保存';
                    statusElement.className = 'batch-status completed';
                }
                
                // 进入下一张或完成
                if (currentFileIndex < imageFiles.length - 1) {
                    currentFileIndex++;
                    loadCurrentImageInBatch();
                    showStatus(`已保存第 ${currentFileIndex} 张，进入下一张`, 'success');
                } else {
                    finishManualBatch();
                }
                
            }, 'image/jpeg', 0.9);
        }
        
        function finishManualBatch() {
            isManualBatchMode = false;
            
            showStatus(`批量手动调整完成！共处理 ${manualBatchResults.length} 张图片`, 'success');
            
            // 显示下载选项
            if (manualBatchResults.length > 0) {
                showManualBatchDownload();
            }
        }
        
        function showManualBatchDownload() {
            const controlGroup = document.querySelector('.control-group:last-child');
            
            // 移除旧的下载按钮
            const existingDownloadArea = document.getElementById('downloadArea');
            if (existingDownloadArea) {
                existingDownloadArea.remove();
            }
            
            const downloadArea = document.createElement('div');
            downloadArea.id = 'downloadArea';
            downloadArea.innerHTML = `
                <h3>📥 手动调整结果下载</h3>
                <button class="btn" onclick="downloadManualBatchAsZip()">📦 打包下载 (ZIP)</button>
                <button class="btn" onclick="downloadManualBatchOneByOne()">📁 逐个下载</button>
                <div style="margin-top: 10px;">
                    <small style="color: #666;">
                        已完成 ${manualBatchResults.length} 张图片的手动调整
                    </small>
                </div>
            `;
            
            controlGroup.appendChild(downloadArea);
        }
        
        function downloadManualBatchAsZip() {
            processedImages = manualBatchResults; // 使用手动调整的结果
            downloadAllAsZip();
        }
        
        function downloadManualBatchOneByOne() {
            processedImages = manualBatchResults; // 使用手动调整的结果
            downloadOneByOne();
        }

        // 下载功能
        function downloadAllAsZip() {
            if (processedImages.length === 0) {
                showStatus('没有可下载的文件', 'error');
                return;
            }

            showStatus('正在打包文件...', 'info');

            // 动态加载JSZip库
            if (typeof JSZip === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/jszip@3/dist/jszip.min.js';
                script.onload = function() {
                    createAndDownloadZip();
                };
                script.onerror = function() {
                    // 如果CDN失败，使用备用方法
                    downloadOneByOne();
                    showStatus('ZIP打包失败，已切换到逐个下载模式', 'info');
                };
                document.head.appendChild(script);
            } else {
                createAndDownloadZip();
            }
        }

        function createAndDownloadZip() {
            const zip = new JSZip();
            
            // 添加所有文件到ZIP
            processedImages.forEach((item, index) => {
                zip.file(item.filename, item.blob);
            });

            // 生成ZIP文件
            zip.generateAsync({type: 'blob'}).then(function(content) {
                const url = URL.createObjectURL(content);
                const a = document.createElement('a');
                a.href = url;
                a.download = '塔罗牌裁剪结果_1030x1795.zip';
                a.click();
                URL.revokeObjectURL(url);
                
                showStatus(`ZIP文件已下载，包含 ${processedImages.length} 张图片`, 'success');
            }).catch(function(error) {
                console.error('ZIP生成失败:', error);
                downloadOneByOne();
                showStatus('ZIP生成失败，已切换到逐个下载模式', 'info');
            });
        }

        function downloadOneByOne() {
            if (processedImages.length === 0) {
                showStatus('没有可下载的文件', 'error');
                return;
            }

            showStatus('开始逐个下载，请允许浏览器多重下载...', 'info');

            // 延迟下载以避免浏览器阻止，使用data URL避免安全警告
            processedImages.forEach((item, index) => {
                setTimeout(() => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const a = document.createElement('a');
                        a.href = e.target.result;
                        a.download = item.filename;
                        
                        // 添加到页面并触发点击
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    };
                    reader.readAsDataURL(item.blob);
                }, index * 300); // 每300ms下载一个文件，给足时间
            });

            setTimeout(() => {
                showStatus(`${processedImages.length} 个文件已开始下载`, 'success');
            }, processedImages.length * 300 + 500);
        }

        // 防止页面拖拽文件时跳转
        document.addEventListener('dragover', e => e.preventDefault());
        document.addEventListener('drop', e => e.preventDefault());
    </script>
</body>
</html>
