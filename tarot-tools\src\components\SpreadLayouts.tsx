'use client';

import TarotCard from './TarotCard';

interface SpreadCard {
  name: string;
  orientation: '正位' | '逆位';
  position: string;
}

interface SpreadLayoutProps {
  spreadType: 'single' | 'three' | 'cross' | 'celtic';
  cards: SpreadCard[];
  isRevealed?: boolean;
}

export default function SpreadLayout({ spreadType, cards, isRevealed = false }: SpreadLayoutProps) {
  
  const renderSingleCard = () => (
    <div className="flex justify-center">
      <div className="text-center">
        <TarotCard
          isRevealed={isRevealed}
          cardName={cards[0]?.name}
          orientation={cards[0]?.orientation}
          size="large"
        />
        <p className="mt-4 text-yellow-300 font-semibold">当前指引</p>
        {/* 调试信息 */}
        {isRevealed && cards[0] && (
          <div className="mt-2 text-xs text-gray-500">
            <p>{cards[0].name}</p>
            <p>{cards[0].orientation}</p>
          </div>
        )}
      </div>
    </div>
  );

  const renderThreeCard = () => (
    <div className="flex justify-center items-end space-x-8">
      {[
        { title: '过去', subtitle: '影响因素' },
        { title: '现在', subtitle: '当前状况' },
        { title: '未来', subtitle: '发展趋势' }
      ].map((position, index) => (
        <div key={index} className="text-center">
          <TarotCard
            isRevealed={isRevealed}
            cardName={cards[index]?.name}
            orientation={cards[index]?.orientation}
            size={index === 1 ? 'large' : 'medium'}
          />
          <div className="mt-4">
            <p className="text-yellow-300 font-semibold">{position.title}</p>
            <p className="text-gray-400 text-sm">{position.subtitle}</p>
            {/* 调试信息 */}
            {isRevealed && cards[index] && (
              <div className="mt-2 text-xs text-gray-500">
                <p>{cards[index].name}</p>
                <p>{cards[index].orientation}</p>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  const renderCrossSpread = () => (
    <div className="relative max-w-md mx-auto">
      {/* 中心卡牌 */}
      <div className="absolute top-16 left-1/2 transform -translate-x-1/2">
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[0]?.name}
            orientation={cards[0]?.orientation}
            size="large"
          />
          <p className="mt-2 text-yellow-300 text-sm font-semibold">核心问题</p>
        </div>
      </div>
      
      {/* 上方卡牌 */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2">
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[1]?.name}
            orientation={cards[1]?.orientation}
            size="medium"
          />
          <p className="mt-2 text-yellow-300 text-xs">未来影响</p>
        </div>
      </div>
      
      {/* 下方卡牌 */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2">
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[2]?.name}
            orientation={cards[2]?.orientation}
            size="medium"
          />
          <p className="mt-2 text-yellow-300 text-xs">基础影响</p>
        </div>
      </div>
      
      {/* 左侧卡牌 */}
      <div className="absolute top-16 left-0">
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[3]?.name}
            orientation={cards[3]?.orientation}
            size="medium"
          />
          <p className="mt-2 text-yellow-300 text-xs">过去影响</p>
        </div>
      </div>
      
      {/* 右侧卡牌 */}
      <div className="absolute top-16 right-0">
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[4]?.name}
            orientation={cards[4]?.orientation}
            size="medium"
          />
          <p className="mt-2 text-yellow-300 text-xs">可能结果</p>
        </div>
      </div>
      
      {/* 占位符，确保容器有足够高度 */}
      <div className="h-80 w-full"></div>
    </div>
  );

  const renderCelticCross = () => (
    <div className="relative max-w-2xl mx-auto">
      {/* 主十字部分 */}
      <div className="relative">
        {/* 中心位置1 - 当前状况 */}
        <div className="absolute top-24 left-32">
          <div className="text-center">
            <TarotCard 
              isRevealed={isRevealed}
              cardName={cards[0]?.name}
              orientation={cards[0]?.orientation}
              size="medium"
            />
            <p className="mt-1 text-yellow-300 text-xs">当前状况</p>
          </div>
        </div>
        
        {/* 位置2 - 挑战(横放在中心上) */}
        <div className="absolute top-28 left-36 transform rotate-90">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[1]?.name}
            orientation={cards[1]?.orientation}
            size="small"
          />
        </div>
        
        {/* 位置3 - 远因 */}
        <div className="absolute top-8 left-32">
          <div className="text-center">
            <TarotCard 
              isRevealed={isRevealed}
              cardName={cards[2]?.name}
              orientation={cards[2]?.orientation}
              size="medium"
            />
            <p className="mt-1 text-yellow-300 text-xs">远因</p>
          </div>
        </div>
        
        {/* 位置4 - 近因 */}
        <div className="absolute top-24 left-16">
          <div className="text-center">
            <TarotCard 
              isRevealed={isRevealed}
              cardName={cards[3]?.name}
              orientation={cards[3]?.orientation}
              size="medium"
            />
            <p className="mt-1 text-yellow-300 text-xs">近因</p>
          </div>
        </div>
        
        {/* 位置5 - 可能发展 */}
        <div className="absolute top-40 left-32">
          <div className="text-center">
            <TarotCard 
              isRevealed={isRevealed}
              cardName={cards[4]?.name}
              orientation={cards[4]?.orientation}
              size="medium"
            />
            <p className="mt-1 text-yellow-300 text-xs">可能发展</p>
          </div>
        </div>
        
        {/* 位置6 - 近期未来 */}
        <div className="absolute top-24 right-16">
          <div className="text-center">
            <TarotCard 
              isRevealed={isRevealed}
              cardName={cards[5]?.name}
              orientation={cards[5]?.orientation}
              size="medium"
            />
            <p className="mt-1 text-yellow-300 text-xs">近期未来</p>
          </div>
        </div>
      </div>
      
      {/* 右侧竖列 */}
      <div className="absolute top-0 right-0 space-y-4">
        {/* 位置7 - 自己的想法 */}
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[6]?.name}
            orientation={cards[6]?.orientation}
            size="medium"
          />
          <p className="mt-1 text-yellow-300 text-xs">自己想法</p>
        </div>
        
        {/* 位置8 - 外界影响 */}
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[7]?.name}
            orientation={cards[7]?.orientation}
            size="medium"
          />
          <p className="mt-1 text-yellow-300 text-xs">外界影响</p>
        </div>
        
        {/* 位置9 - 希望恐惧 */}
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[8]?.name}
            orientation={cards[8]?.orientation}
            size="medium"
          />
          <p className="mt-1 text-yellow-300 text-xs">希望恐惧</p>
        </div>
        
        {/* 位置10 - 最终结果 */}
        <div className="text-center">
          <TarotCard 
            isRevealed={isRevealed}
            cardName={cards[9]?.name}
            orientation={cards[9]?.orientation}
            size="large"
          />
          <p className="mt-1 text-yellow-300 text-sm font-semibold">最终结果</p>
        </div>
      </div>
      
      {/* 占位符 */}
      <div className="h-96 w-full"></div>
    </div>
  );

  switch (spreadType) {
    case 'single':
      return renderSingleCard();
    case 'three':
      return renderThreeCard();
    case 'cross':
      return renderCrossSpread();
    case 'celtic':
      return renderCelticCross();
    default:
      return renderSingleCard();
  }
}
