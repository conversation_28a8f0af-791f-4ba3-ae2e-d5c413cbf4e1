/**
 * 塔罗牌相关类型定义
 */

// 塔罗牌花色
export enum TarotSuit {
  MAJOR_ARCANA = 'major_arcana',    // 大阿卡纳
  WANDS = 'wands',                  // 权杖
  CUPS = 'cups',                    // 圣杯
  SWORDS = 'swords',                // 宝剑
  PENTACLES = 'pentacles'           // 钱币/星币
}

// 塔罗牌方向
export enum TarotOrientation {
  UPRIGHT = 'upright',              // 正位
  REVERSED = 'reversed'             // 逆位
}

// 牌阵类型
export enum SpreadType {
  SINGLE_CARD = 'single_card',                    // 单张牌
  THREE_CARD = 'three_card',                      // 三张牌
  CROSS = 'cross',                                // 十字牌阵
  CELTIC_CROSS = 'celtic_cross',                  // 凯尔特十字
  HORSESHOE = 'horseshoe',                        // 马蹄形
  RELATIONSHIP = 'relationship',                  // 关系牌阵
  YEAR_AHEAD = 'year_ahead',                      // 年运牌阵
  DECISION = 'decision'                           // 决策牌阵
}

// 占卜类型
export enum ReadingType {
  LOVE = 'love',                    // 爱情
  CAREER = 'career',                // 事业
  HEALTH = 'health',                // 健康
  FINANCE = 'finance',              // 财运
  GENERAL = 'general',              // 综合
  SPIRITUAL = 'spiritual',          // 精神/灵性
  RELATIONSHIP = 'relationship'     // 人际关系
}

// 塔罗牌基础信息
export interface TarotCard {
  id: number;                       // 牌的唯一ID
  name: string;                     // 中文名称
  nameEn: string;                   // 英文名称
  suit: TarotSuit;                  // 花色
  number: number | null;            // 牌号 (大阿卡纳为null)
  imageUrl: string;                 // 牌面图片URL
  description: string;              // 牌面描述
  keywords: string[];               // 关键词
  uprightMeaning: string;           // 正位含义
  reversedMeaning: string;          // 逆位含义
  uprightKeywords: string[];        // 正位关键词
  reversedKeywords: string[];       // 逆位关键词
  element?: string;                 // 元素 (火、水、风、土)
  astrology?: string;               // 占星学对应
  numerology?: number;              // 数字学含义
}

// 抽到的塔罗牌 (包含方向)
export interface DrawnCard {
  card: TarotCard;                  // 牌面信息
  orientation: TarotOrientation;    // 正逆位
  position: number;                 // 在牌阵中的位置
  positionMeaning?: string;         // 位置含义
}

// 牌阵配置
export interface SpreadConfig {
  type: SpreadType;                 // 牌阵类型
  name: string;                     // 牌阵名称
  description: string;              // 牌阵描述
  cardCount: number;                // 需要的牌数
  positions: SpreadPosition[];      // 位置配置
  suitable: ReadingType[];          // 适合的占卜类型
}

// 牌阵位置配置
export interface SpreadPosition {
  id: number;                       // 位置ID
  name: string;                     // 位置名称
  meaning: string;                  // 位置含义
  x: number;                        // X坐标 (百分比)
  y: number;                        // Y坐标 (百分比)
  rotation?: number;                // 旋转角度
}

// 占卜记录
export interface TarotReading {
  id: string;                       // 记录ID
  userId?: string;                  // 用户ID
  question: string;                 // 提问
  spreadType: SpreadType;           // 使用的牌阵
  drawnCards: DrawnCard[];          // 抽到的牌
  interpretation: string;           // 解读结果
  readingType: ReadingType;         // 占卜类型
  createdAt: Date;                  // 创建时间
  isPublic: boolean;                // 是否公开
  isFavorite: boolean;              // 是否收藏
}

// 解读选项
export interface InterpretationOptions {
  includePositions: boolean;        // 是否包含位置含义
  includeReversed: boolean;         // 是否考虑逆位
  detailLevel: 'brief' | 'detailed' | 'comprehensive'; // 详细程度
  readingType: ReadingType;         // 占卜类型
  focusAreas?: string[];            // 重点关注领域
}

// 抽牌选项
export interface DrawOptions {
  allowReversed: boolean;           // 是否允许逆位
  shuffleCount: number;             // 洗牌次数
  excludeCards?: number[];          // 排除的牌
}

// 牌组配置
export interface DeckConfig {
  includeMajorArcana: boolean;      // 是否包含大阿卡纳
  includeMinorArcana: boolean;      // 是否包含小阿卡纳
  excludeSuits?: TarotSuit[];       // 排除的花色
  customCards?: number[];           // 自定义牌组
}

// 统计信息
export interface TarotStats {
  totalReadings: number;            // 总占卜次数
  favoriteCard: TarotCard;          // 最常出现的牌
  mostUsedSpread: SpreadType;       // 最常用牌阵
  accuracyRate?: number;            // 准确率 (如果有反馈)
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 错误类型
export interface TarotError {
  code: string;
  message: string;
  details?: any;
}
