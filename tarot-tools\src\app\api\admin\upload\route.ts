import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const cardId = formData.get('cardId') as string;
    const suit = formData.get('suit') as string;

    if (!file) {
      return NextResponse.json({ error: '没有找到文件' }, { status: 400 });
    }

    if (!cardId || !suit) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: '不支持的文件类型' }, { status: 400 });
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json({ error: '文件大小不能超过5MB' }, { status: 400 });
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // 确定文件扩展名
    const fileExtension = file.type === 'image/svg+xml' ? 'svg' : 
                         file.type === 'image/png' ? 'png' : 'jpg';

    // 根据花色确定目录
    const suitDirMap: { [key: string]: string } = {
      'major_arcana': 'major',
      'wands': 'wands',
      'cups': 'cups',
      'swords': 'swords',
      'pentacles': 'pentacles'
    };

    const suitDir = suitDirMap[suit];
    if (!suitDir) {
      return NextResponse.json({ error: '无效的花色' }, { status: 400 });
    }

    // 构建文件路径
    const uploadDir = path.join(process.cwd(), 'public', 'images', 'cards', suitDir);
    
    // 确保目录存在
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // 生成文件名
    let fileName: string;
    if (suit === 'major_arcana') {
      // 大阿卡纳使用两位数字格式
      const cardNumber = parseInt(cardId);
      fileName = `${cardNumber.toString().padStart(2, '0')}-${getCardFileName(cardId, suit)}.${fileExtension}`;
    } else {
      // 小阿卡纳使用特定格式
      fileName = `${getCardFileName(cardId, suit)}.${fileExtension}`;
    }

    const filePath = path.join(uploadDir, fileName);

    // 写入文件
    await writeFile(filePath, buffer);

    // 返回成功响应
    const imageUrl = `/images/cards/${suitDir}/${fileName}`;
    
    return NextResponse.json({
      success: true,
      imageUrl,
      message: '图片上传成功'
    });

  } catch (error) {
    console.error('图片上传错误:', error);
    return NextResponse.json({ error: '图片上传失败' }, { status: 500 });
  }
}

// 根据卡牌ID和花色生成文件名
function getCardFileName(cardId: string, suit: string): string {
  const id = parseInt(cardId);
  
  if (suit === 'major_arcana') {
    const majorNames: { [key: number]: string } = {
      0: 'fool',
      1: 'magician',
      2: 'high-priestess',
      3: 'empress',
      4: 'emperor',
      5: 'hierophant',
      6: 'lovers',
      7: 'chariot',
      8: 'strength',
      9: 'hermit',
      10: 'wheel-of-fortune',
      11: 'justice',
      12: 'hanged-man',
      13: 'death',
      14: 'temperance',
      15: 'devil',
      16: 'tower',
      17: 'star',
      18: 'moon',
      19: 'sun',
      20: 'judgement',
      21: 'world'
    };
    return majorNames[id] || `card-${id}`;
  } else {
    // 小阿卡纳
    const cardNumber = ((id - 101) % 100) + 1;
    if (cardNumber === 1) return 'ace';
    if (cardNumber === 11) return 'page';
    if (cardNumber === 12) return 'knight';
    if (cardNumber === 13) return 'queen';
    if (cardNumber === 14) return 'king';
    return cardNumber.toString().padStart(2, '0');
  }
}
