'use client';

import { useState } from 'react';

interface TarotCardProps {
  isRevealed?: boolean;
  cardName?: string;
  imageUrl?: string;
  orientation?: '正位' | '逆位';
  onClick?: () => void;
  isSelected?: boolean;
  isHovered?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export default function TarotCard({
  isRevealed = false,
  cardName,
  imageUrl,
  orientation,
  onClick,
  isSelected = false,
  isHovered = false,
  size = 'medium'
}: TarotCardProps) {
  const [isFlipping, setIsFlipping] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleClick = () => {
    if (onClick && !isRevealed) {
      setIsFlipping(true);
      setTimeout(() => {
        onClick();
        setIsFlipping(false);
      }, 300);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-16 h-24';
      case 'large':
        return 'w-32 h-48';
      default:
        return 'w-20 h-32';
    }
  };

  return (
    <div
      className={`
        ${getSizeClasses()}
        relative cursor-pointer transform transition-all duration-300
        ${isSelected ? 'scale-110 -translate-y-2' : ''}
        ${isHovered ? 'scale-105' : ''}
        ${isFlipping ? 'scale-95' : ''}
        ${onClick && !isRevealed ? 'hover:scale-105 hover:-translate-y-1' : ''}
      `}
      onClick={handleClick}
    >
      {/* 卡牌阴影 */}
      <div className="absolute inset-0 bg-black/20 rounded-lg transform rotate-1 translate-x-1 translate-y-1"></div>
      
      {/* 主卡牌 */}
      <div className={`
        relative w-full h-full rounded-lg border-2 overflow-hidden
        ${isSelected ? 'border-yellow-400 shadow-lg shadow-yellow-400/30' : 'border-white/20'}
        ${isRevealed ? 'bg-gradient-to-br from-amber-50 to-purple-50' : 'bg-gradient-to-br from-purple-900 to-violet-900'}
        transform transition-all duration-300
      `}>
        
        {isRevealed ? (
          /* 已翻开的卡牌 */
          <div className="w-full h-full relative overflow-hidden">
            {imageUrl && !imageError ? (
              /* 显示真实卡牌图片 */
              <>
                <img
                  src={imageUrl}
                  alt={cardName}
                  className={`w-full h-full object-cover transition-transform duration-300 ${
                    orientation === '逆位' ? 'rotate-180' : ''
                  }`}
                  onError={() => setImageError(true)}
                />

                {/* 卡牌信息覆盖层 */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
                  <div className="text-xs font-bold text-center leading-tight text-white mb-1">
                    {cardName}
                  </div>
                  <div className={`text-xs text-center px-2 py-1 rounded-full font-semibold ${
                    orientation === '正位'
                      ? 'bg-gradient-to-r from-emerald-400 to-emerald-500 text-white'
                      : 'bg-gradient-to-r from-amber-400 to-amber-500 text-white'
                  }`}>
                    {orientation}
                  </div>
                </div>
              </>
            ) : (
              /* 备用显示（图片加载失败或无图片时） */
              <div className="w-full h-full p-2 text-purple-900">
                <div className="w-full h-full border-2 border-amber-300 rounded-lg flex flex-col items-center justify-center bg-gradient-to-br from-amber-50 to-purple-50 shadow-inner">
                  <div className="text-2xl mb-2">🎴</div>
                  <div className="text-xs font-bold text-center leading-tight text-purple-800 px-1">
                    {cardName}
                  </div>
                  <div className={`text-xs mt-2 px-2 py-1 rounded-full font-semibold ${
                    orientation === '正位'
                      ? 'bg-gradient-to-r from-emerald-400 to-emerald-500 text-white shadow-lg'
                      : 'bg-gradient-to-r from-amber-400 to-amber-500 text-white shadow-lg'
                  }`}>
                    {orientation}
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          /* 未翻开的卡牌背面 */
          <div className="w-full h-full relative">
            {/* 神秘图案背景 */}
            <div className="absolute inset-1 bg-gradient-to-br from-purple-800 to-violet-800 rounded-lg">
              {/* 中央神秘符号 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-amber-400 rounded-full flex items-center justify-center animate-pulse">
                  <div className="w-4 h-4 bg-amber-400 rounded-full relative">
                    <div className="absolute inset-0 bg-amber-300 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>

              {/* 装饰图案 */}
              <div className="absolute top-2 left-2 w-1 h-1 bg-amber-400/60 rounded-full animate-pulse delay-100"></div>
              <div className="absolute top-3 right-3 w-1 h-1 bg-amber-400/60 rounded-full animate-pulse delay-200"></div>
              <div className="absolute bottom-2 left-3 w-1 h-1 bg-amber-400/60 rounded-full animate-pulse delay-300"></div>
              <div className="absolute bottom-3 right-2 w-1 h-1 bg-amber-400/60 rounded-full animate-pulse delay-400"></div>

              {/* 边框装饰 */}
              <div className="absolute inset-1 border border-amber-400/30 rounded-lg"></div>
              <div className="absolute inset-2 border border-amber-400/20 rounded-lg"></div>
            </div>

            {/* 选中时的光效 */}
            {isSelected && (
              <div className="absolute inset-0 bg-amber-400/20 rounded-lg animate-glow"></div>
            )}
          </div>
        )}
        
        {/* 点击波纹效果 */}
        {isFlipping && (
          <div className="absolute inset-0 bg-white/30 rounded animate-ping"></div>
        )}
      </div>
    </div>
  );
}
