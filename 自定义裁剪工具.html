<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 自定义裁剪工具 - 尺寸+圆角可调</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .main-content {
            padding: 30px;
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .workspace {
            display: none;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            margin-top: 30px;
        }

        .canvas-area {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: #f9f9f9;
            min-height: 500px;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        #imageCanvas {
            display: block;
            cursor: crosshair;
        }

        .crop-overlay {
            position: absolute;
            border: 3px solid #ff6b6b;
            background: rgba(255, 107, 107, 0.15);
            cursor: move;
            display: none;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
            z-index: 10;
            border-radius: 0px;
            transition: border-radius 0.2s ease;
        }

        .crop-overlay::before {
            content: attr(data-size);
            position: absolute;
            top: -30px;
            left: 0;
            background: #ff6b6b;
            color: white;
            padding: 4px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .crop-overlay::after {
            content: '拖拽移动';
            position: absolute;
            bottom: -30px;
            right: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: visible;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            height: fit-content;
        }

        .control-group {
            margin-bottom: 25px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .control-group h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .input-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .input-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
        }

        .slider-group {
            margin-bottom: 15px;
        }

        .slider-group label {
            display: block;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .slider {
            width: 100%;
            margin: 10px 0;
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }

        .value-display {
            text-align: center;
            font-weight: bold;
            color: #333;
            margin-top: 5px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.95em;
            transition: all 0.3s ease;
            margin: 5px 0;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }

        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .preview-area {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin-bottom: 15px;
        }

        .preview-image {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .info-display {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 0.9em;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .preset-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 15px;
        }

        .preset-btn {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.85em;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .preset-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 自定义裁剪工具</h1>
            <p>自定义尺寸 · 可调圆角 · 专业裁剪</p>
        </div>

        <div class="main-content">
            <!-- 上传区域 -->
            <div class="upload-area" id="uploadArea">
                <div style="font-size: 3em; margin-bottom: 15px;">🎴</div>
                <div style="font-size: 1.2em; margin-bottom: 15px;">拖拽图片到这里，或点击选择文件</div>
                <div style="color: #666; font-size: 0.9em; margin-bottom: 15px;">
                    支持 JPG、PNG 格式 | 自定义尺寸和圆角
                </div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    选择图片文件
                </button>
            </div>

            <!-- 工作区域 -->
            <div class="workspace" id="workspace">
                <div class="canvas-area">
                    <div class="canvas-container">
                        <canvas id="imageCanvas"></canvas>
                        <div class="crop-overlay" id="cropOverlay"></div>
                    </div>
                </div>

                <div class="controls">
                    <!-- 图片信息 -->}
                    <div class="control-group">
                        <h3>📊 图片信息</h3>
                        <div class="info-display">
                            <div class="info-row">
                                <span>文件名:</span>
                                <span id="fileName">-</span>
                            </div>
                            <div class="info-row">
                                <span>原始尺寸:</span>
                                <span id="originalSize">-</span>
                            </div>
                            <div class="info-row">
                                <span>裁剪区域:</span>
                                <span id="cropArea">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- 尺寸设置 -->}
                    <div class="control-group">
                        <h3>📏 自定义尺寸</h3>
                        
                        <!-- 预设尺寸 -->
                        <div class="preset-buttons">
                            <button class="preset-btn" onclick="setPresetSize(1030, 1795)">塔罗牌<br>1030×1795</button>
                            <button class="preset-btn" onclick="setPresetSize(1080, 1620)">标准2:3<br>1080×1620</button>
                            <button class="preset-btn" onclick="setPresetSize(1200, 1600)">4:3比例<br>1200×1600</button>
                            <button class="preset-btn" onclick="setPresetSize(1080, 1080)">正方形<br>1080×1080</button>
                        </div>
                        
                        <div class="input-row">
                            <div class="input-group">
                                <label>宽度 (px)</label>
                                <input type="number" id="widthInput" value="1030" min="100" max="5000" onchange="updateCropSize()">
                            </div>
                            <div class="input-group">
                                <label>高度 (px)</label>
                                <input type="number" id="heightInput" value="1795" min="100" max="5000" onchange="updateCropSize()">
                            </div>
                        </div>
                        
                        <div class="input-row">
                            <div class="input-group">
                                <label>X坐标 (px)</label>
                                <input type="number" id="xInput" value="0" min="0" onchange="updateCropPosition()" placeholder="左上角X坐标">
                            </div>
                            <div class="input-group">
                                <label>Y坐标 (px)</label>
                                <input type="number" id="yInput" value="0" min="0" onchange="updateCropPosition()" placeholder="左上角Y坐标">
                            </div>
                        </div>
                        
                        <div class="input-row">
                            <div class="input-group">
                                <label>宽高比例</label>
                                <input type="text" id="ratioDisplay" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="input-group">
                                <label>比例锁定</label>
                                <input type="checkbox" id="lockRatio" onchange="toggleRatioLock()" style="width: auto; margin-top: 8px;">
                            </div>
                        </div>
                    </div>

                    <!-- 圆角设置 -->}
                    <div class="control-group">
                        <h3>🔄 圆角设置</h3>
                        
                        <div class="slider-group">
                            <label>圆角半径: <span id="radiusValue">0</span>px</label>
                            <input type="range" class="slider" id="radiusSlider" min="0" max="50" value="0" oninput="updateRadius()">
                        </div>
                        
                        <div class="preset-buttons">
                            <button class="preset-btn" onclick="setRadius(0)">直角<br>0px</button>
                            <button class="preset-btn" onclick="setRadius(8)">微圆<br>8px</button>
                            <button class="preset-btn" onclick="setRadius(15)">中圆<br>15px</button>
                            <button class="preset-btn" onclick="setRadius(25)">大圆<br>25px</button>
                        </div>
                    </div>

                    <!-- 裁剪控制 -->}
                    <div class="control-group">
                        <h3>✂️ 裁剪控制</h3>
                        <button class="btn secondary" onclick="resetCropArea()">🔄 重置裁剪框</button>
                        <button class="btn secondary" onclick="centerCropArea()">📍 居中裁剪框</button>
                        <button class="btn" onclick="updatePreview()">👁 更新预览</button>
                        <button class="btn secondary" onclick="autoDetectBounds()">🤖 智能检测边界</button>
                        
                        <!-- 精确定位 -->
                        <div style="margin-top: 15px;">
                            <div style="font-size: 0.9em; color: #666; margin-bottom: 8px;">🎯 精确定位</div>
                            <div class="preset-buttons">
                                <button class="preset-btn" onclick="setPosition('top-left')">左上角</button>
                                <button class="preset-btn" onclick="setPosition('top-right')">右上角</button>
                                <button class="preset-btn" onclick="setPosition('bottom-left')">左下角</button>
                                <button class="preset-btn" onclick="setPosition('bottom-right')">右下角</button>
                            </div>
                        </div>
                    </div>

                    <!-- 预览区域 -->}
                    <div class="control-group">
                        <h3>👁 预览效果</h3>
                        <div class="preview-area">
                            <canvas id="previewCanvas" style="max-width: 100%; border-radius: 8px;"></canvas>
                        </div>
                        <div style="font-size: 0.8em; color: #666; text-align: center;">
                            实际输出尺寸预览
                        </div>
                    </div>

                    <!-- 输出设置 -->}
                    <div class="control-group">
                        <h3>💾 输出设置</h3>
                        
                        <div class="input-group" style="margin-bottom: 15px;">
                            <label>输出格式</label>
                            <select id="formatSelect" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="jpeg">JPEG (小文件)</option>
                                <option value="png">PNG (透明背景)</option>
                                <option value="webp">WebP (最佳压缩)</option>
                            </select>
                        </div>
                        
                        <div class="slider-group">
                            <label>图片质量: <span id="qualityValue">90</span>%</label>
                            <input type="range" class="slider" id="qualitySlider" min="60" max="100" value="90" oninput="updateQuality()">
                        </div>
                        
                        <button class="btn success" onclick="cropAndDownload()">📥 裁剪并下载</button>
                    </div>

                    <!-- 快捷操作 -->}
                    <div class="control-group">
                        <h3>⚡ 快捷操作</h3>
                        <button class="btn secondary" onclick="copyCurrentSettings()">📋 复制当前设置</button>
                        <button class="btn secondary" onclick="saveAsPreset()">💾 保存为预设</button>
                        <button class="btn" onclick="batchProcess()">🚀 批量处理</button>
                    </div>
                </div>
            </div>

            <div id="statusMessage"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentImage = null;
        let currentFile = null;
        let canvas = null;
        let ctx = null;
        let cropOverlay = null;
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let cropArea = { x: 0, y: 0, width: 1030, height: 1795 };
        let lockedRatio = 1030 / 1795;
        let isRatioLocked = false;

        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            canvas = document.getElementById('imageCanvas');
            ctx = canvas.getContext('2d');
            cropOverlay = document.getElementById('cropOverlay');
            
            setupEventListeners();
            updateRatioDisplay();
            showStatus('准备就绪 - 请选择图片文件', 'info');
        }

        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            uploadArea.addEventListener('click', () => {
                fileInput.value = '';
                fileInput.click();
            });
            fileInput.addEventListener('change', handleFileSelect);

            // 裁剪框拖拽
            cropOverlay.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // Canvas点击
            canvas.addEventListener('click', handleCanvasClick);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#667eea';
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#ddd';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                loadImage(files[0]);
            }
        }

        function handleFileSelect(e) {
            if (e.target.files.length > 0) {
                loadImage(e.target.files[0]);
            }
        }

        function loadImage(file) {
            currentFile = file;
            const reader = new FileReader();
            
            reader.onload = function(e) {
                currentImage = new Image();
                currentImage.onload = function() {
                    displayImage();
                    updateImageInfo();
                    // 延迟执行，确保canvas已经渲染
                    setTimeout(() => {
                        resetCropArea();
                        updatePreview();
                    }, 100);
                    document.getElementById('workspace').style.display = 'grid';
                    showStatus(`图片加载成功: ${file.name}`, 'success');
                };
                currentImage.src = e.target.result;
            };
            
            reader.readAsDataURL(file);
        }

        function displayImage() {
            if (!currentImage) return;
            
            const maxWidth = 800;
            const maxHeight = 600;
            
            let displayWidth = currentImage.width;
            let displayHeight = currentImage.height;
            
            if (displayWidth > maxWidth || displayHeight > maxHeight) {
                const scale = Math.min(maxWidth / displayWidth, maxHeight / displayHeight);
                displayWidth *= scale;
                displayHeight *= scale;
            }
            
            canvas.width = displayWidth;
            canvas.height = displayHeight;
            
            // 设置canvas样式，确保显示清晰
            canvas.style.width = displayWidth + 'px';
            canvas.style.height = displayHeight + 'px';
            
            ctx.clearRect(0, 0, displayWidth, displayHeight);
            ctx.drawImage(currentImage, 0, 0, displayWidth, displayHeight);
            
            // 调整canvas容器大小
            const canvasContainer = canvas.parentElement;
            canvasContainer.style.width = displayWidth + 'px';
            canvasContainer.style.height = displayHeight + 'px';
        }

        function updateImageInfo() {
            if (!currentFile || !currentImage) return;
            
            document.getElementById('fileName').textContent = currentFile.name;
            document.getElementById('originalSize').textContent = 
                `${currentImage.width}×${currentImage.height}`;
                
            // 更新XY坐标输入框的最大值限制
            const xInput = document.getElementById('xInput');
            const yInput = document.getElementById('yInput');
            xInput.max = currentImage.width - cropArea.width;
            yInput.max = currentImage.height - cropArea.height;
            
            // 如果当前值超过最大值，则重置为0
            if (parseInt(xInput.value) > parseInt(xInput.max)) {
                xInput.value = 0;
            }
            if (parseInt(yInput.value) > parseInt(yInput.max)) {
                yInput.value = 0;
            }
        }

        function setPresetSize(width, height) {
            document.getElementById('widthInput').value = width;
            document.getElementById('heightInput').value = height;
            updateCropSize();
            
            // 更新按钮状态
            document.querySelectorAll('.preset-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function updateCropSize() {
            const width = parseInt(document.getElementById('widthInput').value);
            const height = parseInt(document.getElementById('heightInput').value);
            
            if (isRatioLocked) {
                // 如果锁定比例，根据宽度自动计算高度
                const newHeight = Math.round(width / lockedRatio);
                document.getElementById('heightInput').value = newHeight;
                cropArea.height = newHeight;
            }
            
            cropArea.width = width;
            cropArea.height = height;
            
            // 更新XY坐标输入框的最大值限制
            if (currentImage) {
                const xInput = document.getElementById('xInput');
                const yInput = document.getElementById('yInput');
                xInput.max = Math.max(0, currentImage.width - cropArea.width);
                yInput.max = Math.max(0, currentImage.height - cropArea.height);
                
                // 如果当前值超过最大值，则调整
                if (parseInt(xInput.value) > parseInt(xInput.max)) {
                    xInput.value = xInput.max;
                }
                if (parseInt(yInput.value) > parseInt(yInput.max)) {
                    yInput.value = yInput.max;
                }
            }
            
            updateRatioDisplay();
            updateCropDisplay();
            updatePreview();
        }

        function updateRatioDisplay() {
            const width = parseInt(document.getElementById('widthInput').value);
            const height = parseInt(document.getElementById('heightInput').value);
            const ratio = (width / height).toFixed(3);
            
            document.getElementById('ratioDisplay').value = `${ratio} (${width}:${height})`;
        }

        function toggleRatioLock() {
            isRatioLocked = document.getElementById('lockRatio').checked;
            if (isRatioLocked) {
                const width = parseInt(document.getElementById('widthInput').value);
                const height = parseInt(document.getElementById('heightInput').value);
                lockedRatio = width / height;
            }
        }

        function updateRadius() {
            const radius = document.getElementById('radiusSlider').value;
            document.getElementById('radiusValue').textContent = radius;
            
            // 实时更新裁剪框的圆角显示
            if (cropOverlay.style.display === 'block') {
                const scale = Math.min(canvas.width / currentImage.width, canvas.height / currentImage.height);
                const scaledRadius = radius * scale;
                cropOverlay.style.borderRadius = scaledRadius + 'px';
            }
            
            updatePreview();
        }
        
        // 新增：精确位置控制函数
        function updateCropPosition() {
            if (!currentImage || !canvas.width) return;
            
            const x = parseInt(document.getElementById('xInput').value) || 0;
            const y = parseInt(document.getElementById('yInput').value) || 0;
            
            // 计算在canvas上的显示位置
            const scale = canvas.width / currentImage.width;
            const displayX = x * scale;
            const displayY = y * scale;
            
            // 计算裁剪框的显示尺寸
            const cropDisplayWidth = Math.min(cropArea.width * scale, canvas.width);
            const cropDisplayHeight = Math.min(cropArea.height * scale, canvas.height);
            
            // 限制在canvas范围内
            const validDisplayX = Math.max(0, Math.min(displayX, canvas.width - cropDisplayWidth));
            const validDisplayY = Math.max(0, Math.min(displayY, canvas.height - cropDisplayHeight));
            
            setCropOverlay(validDisplayX, validDisplayY, cropDisplayWidth, cropDisplayHeight);
            updatePreview();
        }

        function setRadius(value) {
            document.getElementById('radiusSlider').value = value;
            updateRadius();
        }
        
        // 新增：设置裁剪框位置到指定角落
        function setPosition(position) {
            if (!currentImage) {
                showStatus('请先加载图片', 'error');
                return;
            }
            
            let x = 0, y = 0;
            const imageWidth = currentImage.width;
            const imageHeight = currentImage.height;
            
            switch(position) {
                case 'top-left':
                    x = 0;
                    y = 0;
                    break;
                case 'top-right':
                    x = Math.max(0, imageWidth - cropArea.width);
                    y = 0;
                    break;
                case 'bottom-left':
                    x = 0;
                    y = Math.max(0, imageHeight - cropArea.height);
                    break;
                case 'bottom-right':
                    x = Math.max(0, imageWidth - cropArea.width);
                    y = Math.max(0, imageHeight - cropArea.height);
                    break;
            }
            
            // 更新坐标输入框
            document.getElementById('xInput').value = x;
            document.getElementById('yInput').value = y;
            
            // 应用位置
            updateCropPosition();
            
            showStatus(`裁剪框已移动到${position === 'top-left' ? '左上角' : 
                      position === 'top-right' ? '右上角' : 
                      position === 'bottom-left' ? '左下角' : '右下角'}`, 'success');
        }

        function resetCropArea() {
            if (!currentImage) return;
            
            const scale = Math.min(canvas.width / currentImage.width, canvas.height / currentImage.height);
            
            const cropDisplayWidth = Math.min(cropArea.width * scale, canvas.width);
            const cropDisplayHeight = Math.min(cropArea.height * scale, canvas.height);
            
            const x = (canvas.width - cropDisplayWidth) / 2;
            const y = (canvas.height - cropDisplayHeight) / 2;
            
            setCropOverlay(x, y, cropDisplayWidth, cropDisplayHeight);
            updateCropAreaInfo();
            
            showStatus('裁剪框已重置到中心位置', 'success');
        }

        function centerCropArea() {
            resetCropArea();
        }

        function setCropOverlay(x, y, width, height) {
            cropOverlay.style.display = 'block';
            cropOverlay.style.left = x + 'px';
            cropOverlay.style.top = y + 'px';
            cropOverlay.style.width = width + 'px';
            cropOverlay.style.height = height + 'px';
            cropOverlay.style.position = 'absolute';
            cropOverlay.style.zIndex = '10';
            cropOverlay.setAttribute('data-size', `${cropArea.width}×${cropArea.height}`);
            
            // 应用圆角显示
            const radius = document.getElementById('radiusSlider').value;
            if (currentImage) {
                const scale = Math.min(canvas.width / currentImage.width, canvas.height / currentImage.height);
                const scaledRadius = radius * scale;
                cropOverlay.style.borderRadius = scaledRadius + 'px';
            }
            
            // 同步更新XY坐标输入框
            updateCoordinateInputs(x, y);
            
            updateCropAreaInfo();
            // 延迟更新预览，避免过于频繁
            setTimeout(() => updatePreview(), 100);
        }
        
        // 新增：同步更新坐标输入框
        function updateCoordinateInputs(displayX, displayY) {
            if (!currentImage || !canvas.width) return;
            
            // 将显示坐标转换为实际图片坐标
            const scale = currentImage.width / canvas.width;
            const actualX = Math.round(displayX * scale);
            const actualY = Math.round(displayY * scale);
            
            // 更新输入框，但不触发onchange事件
            const xInput = document.getElementById('xInput');
            const yInput = document.getElementById('yInput');
            
            xInput.value = actualX;
            yInput.value = actualY;
        }

        function updateCropDisplay() {
            if (cropOverlay.style.display === 'block') {
                resetCropArea();
            }
        }

        function updateCropAreaInfo() {
            const width = document.getElementById('widthInput').value;
            const height = document.getElementById('heightInput').value;
            document.getElementById('cropArea').textContent = `${width}×${height}`;
        }

        function handleCanvasClick(e) {
            if (!currentImage) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const scale = Math.min(canvas.width / currentImage.width, canvas.height / currentImage.height);
            const cropDisplayWidth = Math.min(cropArea.width * scale, canvas.width);
            const cropDisplayHeight = Math.min(cropArea.height * scale, canvas.height);
            
            const newX = Math.max(0, Math.min(x - cropDisplayWidth/2, canvas.width - cropDisplayWidth));
            const newY = Math.max(0, Math.min(y - cropDisplayHeight/2, canvas.height - cropDisplayHeight));
            
            setCropOverlay(newX, newY, cropDisplayWidth, cropDisplayHeight);
            updatePreview();
        }

        function startDrag(e) {
            isDragging = true;
            dragStart.x = e.clientX;
            dragStart.y = e.clientY;
            e.preventDefault();
        }

        function drag(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - dragStart.x;
            const deltaY = e.clientY - dragStart.y;
            
            const currentLeft = parseInt(cropOverlay.style.left || 0);
            const currentTop = parseInt(cropOverlay.style.top || 0);
            const width = parseInt(cropOverlay.style.width || 0);
            const height = parseInt(cropOverlay.style.height || 0);
            
            // 限制在canvas范围内
            const minX = 0;
            const minY = 0;
            const maxX = canvas.width - width;
            const maxY = canvas.height - height;
            
            const newLeft = Math.max(minX, Math.min(currentLeft + deltaX, maxX));
            const newTop = Math.max(minY, Math.min(currentTop + deltaY, maxY));
            
            cropOverlay.style.left = newLeft + 'px';
            cropOverlay.style.top = newTop + 'px';
            
            // 实时更新坐标输入框
            updateCoordinateInputs(newLeft, newTop);
            
            dragStart.x = e.clientX;
            dragStart.y = e.clientY;
            
            updatePreview();
        }

        function endDrag() {
            isDragging = false;
        }

        function updatePreview() {
            if (!currentImage || cropOverlay.style.display === 'none') return;
            
            const previewCanvas = document.getElementById('previewCanvas');
            const previewCtx = previewCanvas.getContext('2d');
            
            // 设置预览画布尺寸
            const maxPreviewSize = 250;
            const scale = Math.min(maxPreviewSize / cropArea.width, maxPreviewSize / cropArea.height);
            const previewWidth = cropArea.width * scale;
            const previewHeight = cropArea.height * scale;
            
            previewCanvas.width = previewWidth;
            previewCanvas.height = previewHeight;
            previewCanvas.style.width = previewWidth + 'px';
            previewCanvas.style.height = previewHeight + 'px';
            
            // 计算裁剪区域在原图中的位置
            const imageScale = currentImage.width / canvas.width;
            
            const overlayLeft = parseInt(cropOverlay.style.left || 0);
            const overlayTop = parseInt(cropOverlay.style.top || 0);
            
            const sourceX = overlayLeft * imageScale;
            const sourceY = overlayTop * imageScale;
            const sourceWidth = cropArea.width;
            const sourceHeight = cropArea.height;
            
            // 确保源区域在图片范围内
            const validSourceX = Math.max(0, Math.min(sourceX, currentImage.width - sourceWidth));
            const validSourceY = Math.max(0, Math.min(sourceY, currentImage.height - sourceHeight));
            const validSourceWidth = Math.min(sourceWidth, currentImage.width - validSourceX);
            const validSourceHeight = Math.min(sourceHeight, currentImage.height - validSourceY);
            
            // 清空画布
            previewCtx.clearRect(0, 0, previewWidth, previewHeight);
            
            // 裁剪图片
            if (validSourceWidth > 0 && validSourceHeight > 0) {
                previewCtx.drawImage(
                    currentImage,
                    validSourceX, validSourceY, validSourceWidth, validSourceHeight,
                    0, 0, previewWidth, previewHeight
                );
            }
            
            // 应用圆角效果
            const radius = parseInt(document.getElementById('radiusSlider').value);
            if (radius > 0) {
                const scaledRadius = radius * scale;
                applyRoundedCorners(previewCtx, previewWidth, previewHeight, scaledRadius);
            }
        }

        function applyRoundedCorners(ctx, width, height, radius) {
            // 创建圆角遮罩
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, width, height, radius);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
        }

        function cropAndDownload() {
            if (!currentImage) {
                showStatus('请先加载图片', 'error');
                return;
            }
            
            updatePreview(); // 确保预览是最新的
            
            const previewCanvas = document.getElementById('previewCanvas');
            const format = document.getElementById('formatSelect').value;
            const quality = parseInt(document.getElementById('qualitySlider').value) / 100;
            
            const mimeType = {
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'webp': 'image/webp'
            }[format];
            
            previewCanvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                
                const ext = format === 'jpeg' ? 'jpg' : format;
                const radius = document.getElementById('radiusSlider').value;
                const filename = `custom_${cropArea.width}x${cropArea.height}_r${radius}.${ext}`;
                
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
                
                showStatus(`已下载: ${filename}`, 'success');
            }, mimeType, quality);
        }

        function updateQuality() {
            const quality = document.getElementById('qualitySlider').value;
            document.getElementById('qualityValue').textContent = quality;
        }

        function copyCurrentSettings() {
            const settings = {
                width: document.getElementById('widthInput').value,
                height: document.getElementById('heightInput').value,
                x: document.getElementById('xInput').value,
                y: document.getElementById('yInput').value,
                radius: document.getElementById('radiusSlider').value,
                format: document.getElementById('formatSelect').value,
                quality: document.getElementById('qualitySlider').value
            };
            
            const settingsText = `宽度:${settings.width} 高度:${settings.height} X:${settings.x} Y:${settings.y} 圆角:${settings.radius}px 格式:${settings.format} 质量:${settings.quality}%`;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(settingsText);
                showStatus('设置已复制到剪贴板 (包含坐标)', 'success');
            } else {
                showStatus(settingsText, 'info');
            }
        }

        function saveAsPreset() {
            const name = prompt('请输入预设名称:');
            if (name) {
                const preset = {
                    name: name,
                    width: document.getElementById('widthInput').value,
                    height: document.getElementById('heightInput').value,
                    x: document.getElementById('xInput').value,
                    y: document.getElementById('yInput').value,
                    radius: document.getElementById('radiusSlider').value
                };
                
                const presets = JSON.parse(localStorage.getItem('cropPresets') || '[]');
                presets.push(preset);
                localStorage.setItem('cropPresets', JSON.stringify(presets));
                
                showStatus(`预设"${name}"已保存 (包含位置坐标)`, 'success');
            }
        }

        function batchProcess() {
            showStatus('批量处理功能：选择多张图片使用相同设置批量裁剪', 'info');
            
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'image/*';
            
            input.onchange = function(e) {
                const files = Array.from(e.target.files);
                processBatch(files);
            };
            
            input.click();
        }

        function processBatch(files) {
            showStatus(`开始批量处理 ${files.length} 张图片...`, 'info');
            
            let processed = 0;
            
            files.forEach((file, index) => {
                setTimeout(() => {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        canvas.width = cropArea.width;
                        canvas.height = cropArea.height;
                        const ctx = canvas.getContext('2d');
                        
                        // 中心裁剪
                        const sourceX = Math.max(0, (img.width - cropArea.width) / 2);
                        const sourceY = Math.max(0, (img.height - cropArea.height) / 2);
                        
                        ctx.drawImage(
                            img,
                            sourceX, sourceY, cropArea.width, cropArea.height,
                            0, 0, cropArea.width, cropArea.height
                        );
                        
                        // 应用圆角
                        const radius = parseInt(document.getElementById('radiusSlider').value);
                        if (radius > 0) {
                            applyRoundedCorners(ctx, cropArea.width, cropArea.height, radius);
                        }
                        
                        // 下载
                        const format = document.getElementById('formatSelect').value;
                        const quality = parseInt(document.getElementById('qualitySlider').value) / 100;
                        const mimeType = {
                            'jpeg': 'image/jpeg',
                            'png': 'image/png',
                            'webp': 'image/webp'
                        }[format];
                        
                        canvas.toBlob(function(blob) {
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            
                            const ext = format === 'jpeg' ? 'jpg' : format;
                            const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
                            a.download = `${nameWithoutExt}_${cropArea.width}x${cropArea.height}_r${radius}.${ext}`;
                            a.click();
                            URL.revokeObjectURL(url);
                            
                            processed++;
                            if (processed === files.length) {
                                showStatus(`批量处理完成！共处理 ${processed} 张图片`, 'success');
                            }
                        }, mimeType, quality);
                    };
                    
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }, index * 200);
            });
        }

        function autoDetectBounds() {
            if (!currentImage) {
                showStatus('请先加载图片', 'error');
                return;
            }
            
            showStatus('正在智能检测图片边界...', 'info');
            
            // 创建临时canvas分析图片
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = currentImage.width;
            tempCanvas.height = currentImage.height;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(currentImage, 0, 0);
            
            const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
            const data = imageData.data;
            
            let minX = tempCanvas.width, minY = tempCanvas.height, maxX = 0, maxY = 0;
            
            // 寻找非透明且非白色的像素边界
            for (let y = 0; y < tempCanvas.height; y++) {
                for (let x = 0; x < tempCanvas.width; x++) {
                    const index = (y * tempCanvas.width + x) * 4;
                    const r = data[index];
                    const g = data[index + 1];
                    const b = data[index + 2];
                    const a = data[index + 3];
                    
                    // 检查是否为有效内容（非透明且非纯白）
                    if (a > 20 && (r < 250 || g < 250 || b < 250)) {
                        minX = Math.min(minX, x);
                        maxX = Math.max(maxX, x);
                        minY = Math.min(minY, y);
                        maxY = Math.max(maxY, y);
                    }
                }
            }
            
            if (minX < maxX && minY < maxY) {
                // 添加一点边距
                const margin = 20;
                minX = Math.max(0, minX - margin);
                minY = Math.max(0, minY - margin);
                maxX = Math.min(tempCanvas.width, maxX + margin);
                maxY = Math.min(tempCanvas.height, maxY + margin);
                
                const detectedWidth = maxX - minX;
                const detectedHeight = maxY - minY;
                
                // 更新裁剪尺寸
                document.getElementById('widthInput').value = detectedWidth;
                document.getElementById('heightInput').value = detectedHeight;
                updateCropSize();
                
                // 设置裁剪框位置
                const scale = canvas.width / currentImage.width;
                const displayX = minX * scale;
                const displayY = minY * scale;
                const displayWidth = detectedWidth * scale;
                const displayHeight = detectedHeight * scale;
                
                setCropOverlay(displayX, displayY, displayWidth, displayHeight);
                
                showStatus(`智能检测完成！检测到有效区域: ${detectedWidth}×${detectedHeight}`, 'success');
            } else {
                showStatus('未检测到明显的边界，请手动调整', 'error');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 支持CanvasRenderingContext2D.roundRect (for older browsers)
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
