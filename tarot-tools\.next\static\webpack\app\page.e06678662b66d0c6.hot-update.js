"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ai_interpreter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai-interpreter */ \"(app-pages-browser)/./src/lib/ai-interpreter.ts\");\n/* harmony import */ var _constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants/tarot-cards */ \"(app-pages-browser)/./src/constants/tarot-cards.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 简单的文本格式化函数\nfunction formatText(text) {\n    // 按行分割文本\n    const lines = text.split('\\n');\n    return lines.map((line, lineIndex)=>{\n        // 处理标题\n        if (line.startsWith('## ')) {\n            const title = line.slice(3); // 移除 \"## \"\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold text-amber-300 mb-4 mt-6 flex items-center\",\n                children: formatInlineText(title)\n            }, lineIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this);\n        }\n        if (line.startsWith('### ')) {\n            const title = line.slice(4); // 移除 \"### \"\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-amber-200 mb-3 mt-4\",\n                children: formatInlineText(title)\n            }, lineIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this);\n        }\n        // 处理普通段落\n        if (line.trim() === '') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, lineIndex, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-purple-100 leading-relaxed mb-4\",\n            children: formatInlineText(line)\n        }, lineIndex, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    });\n}\n// 处理行内格式（粗体等）\nfunction formatInlineText(text) {\n    const parts = text.split(/(\\*\\*[^*]+\\*\\*)/g);\n    return parts.map((part, index)=>{\n        if (part.startsWith('**') && part.endsWith('**')) {\n            const content = part.slice(2, -2);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"text-amber-200 font-bold\",\n                children: content\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this);\n        }\n        return part;\n    });\n}\nfunction Home() {\n    var _this = this;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('select');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [question, setQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shuffling, setShuffling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deckCards, setDeckCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCards, setSelectedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [revealedCards, setRevealedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maxCards, setMaxCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentReading, setCurrentReading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [interpretingProgress, setInterpretingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [cardLayout, setCardLayout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('circle');\n    const [allCardsFlipped, setAllCardsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [flippedCards, setFlippedCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedCardback, setSelectedCardback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('classic');\n    // 加载用户选择的卡背样式\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (true) {\n                const savedCardback = localStorage.getItem('selectedCardback');\n                if (savedCardback) {\n                    setSelectedCardback(savedCardback);\n                }\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    const startReading = (type)=>{\n        setSelectedType(type);\n        setCurrentStep('question');\n        // 设置牌数\n        const cardCounts = {\n            'single': 1,\n            'three': 3,\n            'love': 3,\n            'career': 3,\n            'cross': 5,\n            'horseshoe': 7,\n            'celtic': 10,\n            'star': 7,\n            'pyramid': 6,\n            'moon': 4,\n            'chakra': 7,\n            'decision': 5\n        };\n        setMaxCards(cardCounts[type]);\n    };\n    const startPreview = ()=>{\n        setCurrentStep('preview');\n        setAllCardsFlipped(false);\n        setFlippedCards(new Set());\n    };\n    const startCardbackSelection = ()=>{\n        setCurrentStep('cardback');\n    };\n    const selectCardback = (style)=>{\n        setSelectedCardback(style);\n        // 保存到localStorage\n        if (true) {\n            localStorage.setItem('selectedCardback', style);\n        }\n    };\n    // 渲染不同风格的卡背\n    const renderCardback = function(style) {\n        let isPreview = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, isSmall = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const baseClasses = isPreview ? \"w-32 h-48 mx-auto\" : \"w-full h-full\";\n        const symbolSize = isSmall ? isPreview ? \"text-lg\" : \"text-xs\" : isPreview ? \"text-xl\" : \"text-lg\";\n        const decorSize = isSmall ? \"text-xs\" : isPreview ? \"text-sm\" : \"text-xs\";\n        switch(style){\n            case 'classic':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border-2 border-amber-400/40 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-amber-300 \".concat(symbolSize),\n                                children: \"⚜\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 right-2 text-amber-400/60 \".concat(decorSize),\n                            children: \"✦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, _this);\n            case 'luxury':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-black overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-amber-400/80 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-amber-400/60 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-12 h-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border-2 border-amber-400/70 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-2 border border-amber-400/60 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-amber-400 \".concat(symbolSize),\n                                            children: \"\\uD83C\\uDF1F\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-1/2 transform -translate-x-1/2 text-amber-400/80 text-xs tracking-widest\",\n                            children: \"TAROT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-amber-400/80 text-xs tracking-widest\",\n                            children: \"MYSTICAL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 11\n                }, _this);\n            case 'sacred':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-slate-900 via-purple-950 to-indigo-950 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(251,191,36,0.15)_0%,transparent_60%)]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-amber-400/50 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-10 h-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 text-amber-400/80 \".concat(symbolSize, \" flex items-center justify-center\"),\n                                        children: \"\\uD83D\\uDD4E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-2 border border-amber-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 left-3 text-amber-400/60 text-xs\",\n                            children: \"✡\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 right-3 text-amber-400/60 text-xs\",\n                            children: \"☪\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 left-3 text-amber-400/60 text-xs\",\n                            children: \"☯\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 right-3 text-amber-400/60 text-xs\",\n                            children: \"\\uD83D\\uDD2F\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, _this);\n            case 'cosmic':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-indigo-950 via-blue-950 to-purple-950 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(59,130,246,0.1)_0%,transparent_70%)]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-blue-400/50 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-300 \".concat(symbolSize, \" animate-pulse\"),\n                                    children: \"\\uD83C\\uDF0C\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 text-blue-400/60 text-xs\",\n                            children: \"✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 text-blue-400/60 text-xs\",\n                            children: \"⭐\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-2 text-blue-400/60 text-xs\",\n                            children: \"\\uD83C\\uDF19\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 right-2 text-blue-400/60 text-xs\",\n                            children: \"☄️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-blue-300/80 text-xs tracking-wider\",\n                            children: \"COSMIC\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, _this);\n            case 'elegant':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border border-gray-300/40 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-8 h-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border border-gray-300/60 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-1 bg-gradient-to-br from-gray-300/20 to-gray-400/20 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 \".concat(symbolSize),\n                                            children: \"◇\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 left-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 right-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 left-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-3 right-3 text-gray-400/50 text-xs\",\n                            children: \"◆\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-gray-300/70 text-xs tracking-widest\",\n                            children: \"ELEGANT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 11\n                }, _this);\n            case 'royal':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-purple-900 via-indigo-900 to-purple-900 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-yellow-400/60 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 border border-yellow-400/40 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-yellow-400 \".concat(symbolSize),\n                                    children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 text-yellow-400/60 text-xs\",\n                            children: \"♠\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 text-yellow-400/60 text-xs\",\n                            children: \"♥\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-2 text-yellow-400/60 text-xs\",\n                            children: \"♣\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 right-2 text-yellow-400/60 text-xs\",\n                            children: \"♦\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-yellow-400/80 text-xs tracking-widest\",\n                            children: \"ROYAL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, _this);\n            case 'minimal':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative bg-gradient-to-br from-slate-800 to-slate-900 overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-2 border border-white/30 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/80 \".concat(symbolSize),\n                                children: \"◯\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 text-white/60 text-xs tracking-widest\",\n                            children: \"MINIMAL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, _this);\n            case 'ai-generated':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(baseClasses, \" relative overflow-hidden rounded-lg\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-radial from-amber-400/10 via-transparent to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(251,191,36,0.1)_0%,transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(251,191,36,0.1)_0%,transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-contain bg-center bg-no-repeat\",\n                            style: {\n                                backgroundImage: 'url(/images/cardback-ai.png)'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-amber-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 11\n                }, _this);\n            default:\n                return renderCardback('classic', isPreview);\n        }\n    };\n    const toggleAllCards = ()=>{\n        const newFlipped = !allCardsFlipped;\n        setAllCardsFlipped(newFlipped);\n        if (newFlipped) {\n            // 全部翻到正面\n            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n            setFlippedCards(new Set(allCards.map((card)=>card.id.toString())));\n        } else {\n            // 全部翻到反面\n            setFlippedCards(new Set());\n        }\n    };\n    const toggleSingleCard = (cardId)=>{\n        const newFlippedCards = new Set(flippedCards);\n        if (newFlippedCards.has(cardId)) {\n            newFlippedCards.delete(cardId);\n        } else {\n            newFlippedCards.add(cardId);\n        }\n        setFlippedCards(newFlippedCards);\n        // 检查是否所有卡牌都翻开了\n        const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n        const allFlipped = newFlippedCards.size === allCards.length;\n        setAllCardsFlipped(allFlipped);\n    };\n    const startShuffle = ()=>{\n        setCurrentStep('shuffle');\n        setShuffling(true);\n        // 生成牌组 - 使用实际的78张牌\n        const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n        const cards = Array.from({\n            length: allCards.length\n        }, (_, i)=>i);\n        setDeckCards(cards);\n        // 模拟洗牌过程\n        setTimeout(()=>{\n            setShuffling(false);\n            setCurrentStep('draw');\n        }, 3000);\n    };\n    const selectCard = (cardIndex)=>{\n        if (selectedCards.includes(cardIndex) || selectedCards.length >= maxCards) return;\n        const newSelectedCards = [\n            ...selectedCards,\n            cardIndex\n        ];\n        setSelectedCards(newSelectedCards);\n        // 如果选够了牌，立即跳转到解读界面并开始AI解读\n        if (newSelectedCards.length === maxCards) {\n            setTimeout(()=>{\n                setCurrentStep('interpreting');\n                performReading(newSelectedCards);\n            }, 800);\n        }\n    };\n    const performReading = async (cardIndices)=>{\n        try {\n            // 重置进度\n            setInterpretingProgress(0);\n            // 模拟进度更新\n            const progressInterval = setInterval(()=>{\n                setInterpretingProgress((prev)=>{\n                    if (prev >= 90) {\n                        clearInterval(progressInterval);\n                        return 90; // 保持在90%，等AI完成后再到100%\n                    }\n                    // 确保进度不会超过90%\n                    const increment = Math.random() * 8 + 2; // 2-10的随机增量\n                    return Math.min(prev + increment, 90);\n                });\n            }, 500);\n            // 根据选中的牌索引生成塔罗牌数据\n            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n            // 加载已上传的图片\n            const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n            const selectedTarotCards = cardIndices.map((index, position)=>{\n                // 从完整的78张牌中选择\n                const cardData = allCards[index % allCards.length];\n                const orientation = Math.random() > 0.5 ? '正位' : '逆位';\n                return {\n                    name: cardData.name,\n                    imageUrl: uploadedImages[cardData.id] || cardData.imageUrl,\n                    orientation,\n                    position: getPositionName(position)\n                };\n            });\n            // 设置显示的牌\n            setRevealedCards(selectedTarotCards);\n            // 准备AI解读请求\n            const spreadTypes = {\n                'single': '单张牌占卜',\n                'three': '三张牌占卜',\n                'love': '爱情三张牌占卜',\n                'career': '事业三张牌占卜',\n                'cross': '十字牌阵',\n                'celtic': '凯尔特十字牌阵',\n                'horseshoe': '马蹄铁牌阵',\n                'star': '七芒星牌阵',\n                'pyramid': '金字塔牌阵',\n                'moon': '月相牌阵',\n                'chakra': '脉轮牌阵',\n                'decision': '决策牌阵'\n            };\n            const readingTypes = {\n                'single': '快速指引',\n                'three': '过去现在未来',\n                'love': '爱情运势',\n                'career': '事业发展',\n                'cross': '综合运势',\n                'celtic': '深度解读',\n                'horseshoe': '七个层面解读',\n                'star': '能量流动解读',\n                'pyramid': '层次递进解读',\n                'moon': '情感周期解读',\n                'chakra': '能量中心解读',\n                'decision': '选择路径解读'\n            };\n            const aiRequest = {\n                question,\n                cards: selectedTarotCards,\n                spreadType: spreadTypes[selectedType] || '塔罗占卜',\n                readingType: readingTypes[selectedType] || '综合解读'\n            };\n            // 调用AI解读\n            const aiResult = await (0,_lib_ai_interpreter__WEBPACK_IMPORTED_MODULE_2__.getAIInterpretation)(aiRequest);\n            // 完成进度并跳转\n            setInterpretingProgress(100);\n            setTimeout(()=>{\n                setCurrentReading({\n                    interpretation: aiResult.interpretation,\n                    error: aiResult.success ? null : aiResult.error || '解读生成失败'\n                });\n                setCurrentStep('result');\n            }, 500);\n        } catch (error) {\n            console.error('占卜过程出错:', error);\n            setInterpretingProgress(100);\n            setTimeout(()=>{\n                setCurrentReading({\n                    interpretation: '',\n                    error: '占卜过程中出现了问题，请重试。'\n                });\n                setCurrentStep('result');\n            }, 500);\n        }\n    };\n    const resetReading = ()=>{\n        setCurrentStep('select');\n        setSelectedType(null);\n        setQuestion('');\n        setDeckCards([]);\n        setSelectedCards([]);\n        setRevealedCards([]);\n        setCurrentReading(null);\n        setInterpretingProgress(0);\n        setAllCardsFlipped(false);\n        setFlippedCards(new Set());\n    };\n    // 渲染单个卡牌\n    const renderCard = (i, customStyle)=>{\n        const isGrid = cardLayout === 'grid';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>selectCard(i),\n            onMouseEnter: ()=>setHoveredCard(i),\n            onMouseLeave: ()=>setHoveredCard(null),\n            className: \"\".concat(isGrid ? 'relative' : 'absolute', \" cursor-pointer transform transition-all duration-300 \").concat(selectedCards.includes(i) ? 'scale-125 z-20' : hoveredCard === i ? 'scale-110 z-10' : 'hover:scale-105', \" \").concat(selectedCards.length >= maxCards && !selectedCards.includes(i) ? 'opacity-30 cursor-not-allowed' : '', \" \").concat(isGrid && selectedCards.includes(i) ? 'drop-shadow-[0_12px_25px_rgba(251,191,36,0.8)]' : isGrid && hoveredCard === i ? 'drop-shadow-[0_6px_15px_rgba(251,191,36,0.4)]' : isGrid ? 'drop-shadow-[0_3px_8px_rgba(0,0,0,0.2)]' : ''),\n            style: customStyle,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(isGrid ? 'w-[63px] h-[96px]' : 'w-[49px] h-[73px]', \" rounded-[6px] border transition-all duration-300 overflow-hidden \").concat(selectedCards.includes(i) ? 'border-amber-400 shadow-lg shadow-amber-400/30' : 'border-amber-400'),\n                    children: selectedCards.includes(i) ? // 已选中状态 - 发光效果\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full bg-gradient-to-br from-amber-400/40 to-amber-600/40 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-amber-300 \".concat(isGrid ? 'text-lg' : 'text-sm'),\n                            children: \"✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 13\n                    }, this) : // 未选中状态 - 使用用户选择的卡背样式\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full\",\n                        children: renderCardback(selectedCardback, false, !isGrid)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, this),\n                selectedCards.includes(i) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute \".concat(isGrid ? '-top-2 -right-2 w-6 h-6' : '-top-1 -right-1 w-5 h-5', \" bg-amber-400 rounded-full flex items-center justify-center text-xs font-bold text-purple-900\"),\n                    children: selectedCards.indexOf(i) + 1\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, i, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 482,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染不同的卡牌布局\n    const renderCardLayout = ()=>{\n        const totalCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)().length;\n        if (cardLayout === 'circle') {\n            // 圆形布局\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-4xl mx-auto h-96 mb-8 animate-in fade-in duration-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-80 h-80\",\n                        children: [\n                            Array.from({\n                                length: totalCards\n                            }, (_, i)=>{\n                                const angleStep = 360 / totalCards;\n                                const angle = i * angleStep;\n                                const radius = 130;\n                                const radian = angle * Math.PI / 180;\n                                const x = Math.cos(radian) * radius;\n                                const y = Math.sin(radian) * radius;\n                                return renderCard(i, {\n                                    left: \"calc(50% + \".concat(x, \"px)\"),\n                                    top: \"calc(50% + \".concat(y, \"px)\"),\n                                    transform: \"translate(-50%, -50%) rotate(\".concat(angle + 90, \"deg) \").concat(selectedCards.includes(i) ? 'scale(1.25)' : hoveredCard === i ? 'scale(1.1)' : ''),\n                                    zIndex: selectedCards.includes(i) ? 20 : hoveredCard === i ? 10 : 1,\n                                    filter: selectedCards.includes(i) ? 'drop-shadow(0 12px 25px rgba(251, 191, 36, 0.8))' : hoveredCard === i ? 'drop-shadow(0 6px 15px rgba(251, 191, 36, 0.4))' : 'drop-shadow(0 3px 8px rgba(0, 0, 0, 0.2))',\n                                    transition: 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)'\n                                });\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-gradient-to-br from-purple-900 to-violet-900 rounded-full border-2 border-amber-400/50 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl animate-pulse\",\n                                    children: \"\\uD83D\\uDD2E\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 542,\n                columnNumber: 9\n            }, this);\n        } else {\n            // 默认：重叠排列布局 (grid)\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full mb-8 animate-in fade-in duration-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex overflow-x-auto pb-4 pt-8 px-8\",\n                        style: {\n                            scrollbarWidth: 'thin',\n                            scrollbarColor: '#d97706 transparent'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: Array.from({\n                                length: totalCards\n                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 animate-in slide-in-from-bottom duration-500\",\n                                    style: {\n                                        marginLeft: i === 0 ? '0' : '-50px',\n                                        zIndex: selectedCards.includes(i) ? 100 : totalCards - i,\n                                        animationDelay: \"\".concat(i * 20, \"ms\")\n                                    },\n                                    children: renderCard(i)\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-purple-300 bg-purple-800/20 px-4 py-2 rounded-full border border-amber-400/20\",\n                            children: \"← 滑动查看所有牌卡 →\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 578,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    const getSpreadType = ()=>{\n        if (selectedType === 'single') return 'single';\n        if (selectedType === 'three' || selectedType === 'love' || selectedType === 'career') return 'three';\n        if (selectedType === 'cross') return 'cross';\n        if (selectedType === 'celtic') return 'celtic';\n        return 'single';\n    };\n    const getPositionName = (index)=>{\n        const spreadType = getSpreadType();\n        if (spreadType === 'single') {\n            return '当前指引';\n        } else if (spreadType === 'three') {\n            return [\n                '过去',\n                '现在',\n                '未来'\n            ][index] || \"第\".concat(index + 1, \"张牌\");\n        }\n        return \"第\".concat(index + 1, \"张牌\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-indigo-950 via-purple-900 to-violet-950 text-white overflow-hidden relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] left-[15%] w-1 h-1 bg-amber-300 rounded-full animate-pulse shadow-lg shadow-amber-300/50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[25%] right-[20%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-1000 shadow-sm shadow-amber-200/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[8%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-500 shadow-lg shadow-amber-400/60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[60%] right-[12%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[75%] left-[25%] w-1 h-1 bg-amber-200 rounded-full animate-pulse delay-2000 shadow-md shadow-amber-200/40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[35%] right-[35%] w-0.5 h-0.5 bg-amber-400 rounded-full animate-pulse delay-700\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[80%] right-[40%] w-1 h-1 bg-amber-300 rounded-full animate-pulse delay-1200 shadow-lg shadow-amber-300/50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[15%] left-[60%] w-0.5 h-0.5 bg-amber-200 rounded-full animate-pulse delay-800\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[50%] left-[70%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse delay-300 shadow-lg shadow-amber-400/60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[90%] left-[45%] w-0.5 h-0.5 bg-amber-300 rounded-full animate-pulse delay-1800\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[20%] left-[30%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-60 animate-pulse delay-2500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[70%] right-[60%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-40 animate-pulse delay-3000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[45%] left-[85%] w-0.5 h-0.5 bg-amber-100 rounded-full opacity-50 animate-pulse delay-2200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 651,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 right-1/4 w-80 h-80 bg-amber-400/5 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 652,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 py-8\",\n                children: [\n                    currentStep !== 'select' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: resetReading,\n                                className: \"group flex items-center space-x-3 bg-gradient-to-r from-purple-800/30 to-violet-800/30 hover:from-purple-700/40 hover:to-violet-700/40 backdrop-blur-md rounded-2xl px-8 py-4 transition-all duration-300 border border-amber-400/20 hover:border-amber-400/40 shadow-lg hover:shadow-amber-400/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-amber-300 group-hover:text-amber-200 transition-colors text-lg\",\n                                        children: \"←\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-amber-100 group-hover:text-white transition-colors font-medium\",\n                                        children: \"重新开始\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full transition-all duration-500 \".concat(i <= [\n                                                    'select',\n                                                    'question',\n                                                    'shuffle',\n                                                    'draw',\n                                                    'result',\n                                                    'preview',\n                                                    'cardback'\n                                                ].indexOf(currentStep) ? 'bg-gradient-to-r from-amber-400 to-amber-300 shadow-lg shadow-amber-400/50' : 'bg-purple-300/20 border border-purple-300/30')\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-amber-200 font-medium bg-purple-800/20 px-4 py-2 rounded-full border border-amber-400/20\",\n                                        children: {\n                                            'select': '✨ 选择类型',\n                                            'question': '💭 输入问题',\n                                            'spread': '🔮 准备占卜',\n                                            'shuffle': '🌀 洗牌',\n                                            'draw': '🎴 选牌',\n                                            'result': '📜 解读结果',\n                                            'interpreting': '🤖 AI解读中',\n                                            'preview': '🎴 预览塔罗牌',\n                                            'cardback': '🎨 卡背设计'\n                                        }[currentStep]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-32\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 13\n                            }, this),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'select' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative inline-block mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-amber-400/20 to-purple-400/20 blur-3xl rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative text-8xl mb-6 animate-pulse\",\n                                                children: \"\\uD83D\\uDD2E\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-2 -right-2 text-2xl animate-spin-slow\",\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-2 -left-2 text-xl animate-bounce\",\n                                                children: \"\\uD83C\\uDF19\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-6xl font-bold mb-8 bg-gradient-to-r from-amber-300 via-amber-200 to-amber-100 bg-clip-text text-transparent tracking-wider drop-shadow-lg\",\n                                        children: \"神秘塔罗\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-purple-200 mb-12 tracking-wide font-light\",\n                                        children: \"✨ 探索命运的奥秘 \\xb7 窥见未来的真相 ✨\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-5xl mx-auto bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-10 border border-amber-400/20 shadow-2xl shadow-purple-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold mb-8 text-amber-300 text-center\",\n                                                children: \"✨ 塔罗占卜指引 ✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-8 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83C\\uDFAF\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"专注你的问题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"在内心深处思考你想要了解的事情，让宇宙感受到你的诚意\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 714,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83C\\uDF00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"神秘洗牌仪式\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"观看古老的塔罗牌洗牌过程，感受神秘力量的流动\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83D\\uDC46\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"凭直觉选牌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"从78张牌中选择命运为你准备的牌，相信你的第一感觉\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl group-hover:scale-110 transition-transform duration-300\",\n                                                                children: \"\\uD83D\\uDCDC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-amber-100 mb-2\",\n                                                                        children: \"专业解读\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200 text-sm leading-relaxed\",\n                                                                        children: \"获得深度的塔罗牌解读和人生指引，照亮前行的道路\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-center mb-16 text-amber-300\",\n                                        children: \"\\uD83D\\uDD2E 选择你的占卜类型 \\uD83D\\uDD2E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                        children: [\n                                            {\n                                                type: 'preview',\n                                                icon: '🎴',\n                                                title: '预览塔罗牌',\n                                                desc: '查看78张塔罗牌',\n                                                tag: '预览模式 · 实用',\n                                                color: 'from-emerald-400 to-emerald-600',\n                                                special: true\n                                            },\n                                            {\n                                                type: 'cardback',\n                                                icon: '🎨',\n                                                title: '卡背设计',\n                                                desc: '选择塔罗牌背面',\n                                                tag: '设计选择 · 个性',\n                                                color: 'from-rose-400 to-rose-600',\n                                                special: true\n                                            },\n                                            {\n                                                type: 'single',\n                                                icon: '🌟',\n                                                title: '单牌指引',\n                                                desc: '简单直接的答案',\n                                                tag: '1张牌 · 初级',\n                                                color: 'from-amber-400 to-amber-600'\n                                            },\n                                            {\n                                                type: 'three',\n                                                icon: '⏳',\n                                                title: '时间三牌',\n                                                desc: '过去-现在-未来',\n                                                tag: '3张牌 · 推荐',\n                                                color: 'from-purple-400 to-purple-600'\n                                            },\n                                            {\n                                                type: 'cross',\n                                                icon: '✚',\n                                                title: '十字占卜',\n                                                desc: '核心-影响-结果',\n                                                tag: '5张牌 · 中级',\n                                                color: 'from-violet-400 to-violet-600'\n                                            },\n                                            {\n                                                type: 'celtic',\n                                                icon: '🎯',\n                                                title: '凯尔特十字',\n                                                desc: '最详细的全面解读',\n                                                tag: '10张牌 · 高级',\n                                                color: 'from-indigo-400 to-indigo-600'\n                                            },\n                                            {\n                                                type: 'love',\n                                                icon: '💕',\n                                                title: '爱情解读',\n                                                desc: '专门的情感占卜',\n                                                tag: '3张牌 · 专题',\n                                                color: 'from-pink-400 to-pink-600'\n                                            },\n                                            {\n                                                type: 'career',\n                                                icon: '💼',\n                                                title: '事业指导',\n                                                desc: '职场发展方向',\n                                                tag: '3张牌 · 专题',\n                                                color: 'from-emerald-400 to-emerald-600'\n                                            },\n                                            {\n                                                type: 'horseshoe',\n                                                icon: '🐎',\n                                                title: '马蹄铁牌阵',\n                                                desc: '七个层面的深度分析',\n                                                tag: '7张牌 · 高级',\n                                                color: 'from-orange-400 to-orange-600'\n                                            },\n                                            {\n                                                type: 'star',\n                                                icon: '⭐',\n                                                title: '七芒星牌阵',\n                                                desc: '能量流动与平衡',\n                                                tag: '7张牌 · 神秘',\n                                                color: 'from-cyan-400 to-cyan-600'\n                                            },\n                                            {\n                                                type: 'pyramid',\n                                                icon: '🔺',\n                                                title: '金字塔牌阵',\n                                                desc: '层次递进的智慧',\n                                                tag: '6张牌 · 中级',\n                                                color: 'from-yellow-400 to-yellow-600'\n                                            },\n                                            {\n                                                type: 'moon',\n                                                icon: '🌙',\n                                                title: '月相牌阵',\n                                                desc: '情感周期与直觉',\n                                                tag: '4张牌 · 灵性',\n                                                color: 'from-blue-400 to-blue-600'\n                                            },\n                                            {\n                                                type: 'chakra',\n                                                icon: '🧘',\n                                                title: '脉轮牌阵',\n                                                desc: '七大能量中心',\n                                                tag: '7张牌 · 疗愈',\n                                                color: 'from-teal-400 to-teal-600'\n                                            },\n                                            {\n                                                type: 'decision',\n                                                icon: '⚖️',\n                                                title: '决策牌阵',\n                                                desc: '选择与后果分析',\n                                                tag: '5张牌 · 实用',\n                                                color: 'from-slate-400 to-slate-600'\n                                            }\n                                        ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"group cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2\",\n                                                onClick: ()=>{\n                                                    if (item.type === 'preview') {\n                                                        startPreview();\n                                                    } else if (item.type === 'cardback') {\n                                                        startCardbackSelection();\n                                                    } else {\n                                                        startReading(item.type);\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gradient-to-br from-purple-900/60 to-violet-900/60 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 shadow-2xl overflow-hidden hover:shadow-amber-400/20 transition-all duration-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-br from-amber-400/5 to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 text-amber-300 opacity-60 group-hover:opacity-100 transition-opacity duration-300\",\n                                                            children: \"✨\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 text-amber-300 opacity-40 group-hover:opacity-80 transition-opacity duration-300\",\n                                                            children: \"\\uD83C\\uDF19\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative z-10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300\",\n                                                                    children: item.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold mb-3 text-center text-amber-100 group-hover:text-amber-50 transition-colors duration-300\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-purple-200 text-center mb-6 leading-relaxed text-sm\",\n                                                                    children: item.desc\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-block px-4 py-2 bg-gradient-to-r \".concat(item.color, \" rounded-full text-sm font-semibold text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300\"),\n                                                                        children: item.tag\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 rounded-3xl border border-amber-400/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, item.type, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 863,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'question' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83D\\uDCAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"请输入你的问题\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: \"让塔罗牌为你揭示答案\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: question,\n                                        onChange: (e)=>setQuestion(e.target.value),\n                                        placeholder: \"请输入你想要了解的问题...\",\n                                        className: \"w-full h-32 bg-purple-800/30 border border-amber-400/30 rounded-2xl p-4 text-white placeholder-purple-300 focus:outline-none focus:border-amber-400/60 transition-colors resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: startShuffle,\n                                            disabled: !question.trim(),\n                                            className: \"bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 disabled:from-gray-600 disabled:to-gray-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 disabled:scale-100 shadow-lg\",\n                                            children: \"开始占卜 ✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 927,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 13\n                            }, this),\n                            (()=>{\n                                const spreadDetails = {\n                                    'single': {\n                                        icon: '🌟',\n                                        title: '单牌指引',\n                                        description: '最简单直接的占卜方式，适合快速获得指引和建议。一张牌能够提供清晰明确的答案，特别适合日常决策和即时问题。',\n                                        usage: '适用于：日常选择、当下困惑、快速指引、简单问题',\n                                        examples: '今天我应该做什么？这个决定对我有利吗？我现在的状态如何？',\n                                        tips: '问题要具体明确，避免过于复杂的多重问题'\n                                    },\n                                    'three': {\n                                        icon: '⏳',\n                                        title: '时间三牌',\n                                        description: '经典的过去-现在-未来牌阵，帮你了解事情的发展脉络。通过三个时间维度，全面分析问题的来龙去脉和发展趋势。',\n                                        usage: '适用于：了解发展趋势、分析问题根源、预测未来走向、制定计划',\n                                        examples: '我的感情会如何发展？这个项目的前景怎样？我的职业规划如何？',\n                                        tips: '适合需要了解事情发展过程的问题，可以是任何领域的问题'\n                                    },\n                                    'cross': {\n                                        icon: '✚',\n                                        title: '十字占卜',\n                                        description: '五张牌组成的十字形牌阵，深入分析问题的核心、影响因素和最终结果。提供全面而平衡的视角。',\n                                        usage: '适用于：复杂问题分析、多角度思考、寻找解决方案、重要决策',\n                                        examples: '我应该换工作吗？这段关系值得继续吗？如何解决当前的困境？',\n                                        tips: '适合需要深入分析的重要问题，会从多个角度给出建议'\n                                    },\n                                    'celtic': {\n                                        icon: '🎯',\n                                        title: '凯尔特十字',\n                                        description: '最经典和详细的塔罗牌阵，十张牌提供最全面的解读。涵盖问题的各个方面，包括潜意识、外在影响、内在力量等。',\n                                        usage: '适用于：人生重大问题、全面生活分析、深度自我探索、复杂情况',\n                                        examples: '我的人生方向是什么？如何实现我的目标？我该如何面对人生转折？',\n                                        tips: '适合最重要的人生问题，需要充足时间来理解和消化解读结果'\n                                    },\n                                    'love': {\n                                        icon: '💕',\n                                        title: '爱情解读',\n                                        description: '专门针对感情问题设计的三牌阵，分析你的感情状态、对方的想法和关系的发展。特别适合情感困惑。',\n                                        usage: '适用于：恋爱关系、婚姻问题、暗恋困扰、分手复合、感情选择',\n                                        examples: '他/她对我有感觉吗？我们的关系会有结果吗？我应该表白吗？',\n                                        tips: '专注于感情相关问题，可以询问具体的感情状况和发展'\n                                    },\n                                    'career': {\n                                        icon: '💼',\n                                        title: '事业指导',\n                                        description: '专门分析职业发展的三牌阵，从当前状况、发展机会和行动建议三个角度指导你的职业规划。',\n                                        usage: '适用于：职业选择、工作转换、升职加薪、创业决策、职场关系',\n                                        examples: '我应该跳槽吗？如何在职场上获得成功？这个投资项目可行吗？',\n                                        tips: '专注于事业和财务相关问题，可以询问具体的职业发展策略'\n                                    },\n                                    'horseshoe': {\n                                        icon: '🐎',\n                                        title: '马蹄铁牌阵',\n                                        description: '七张牌组成的马蹄形牌阵，提供七个层面的深度分析。从过去影响到未来结果，全面解读问题的各个维度。',\n                                        usage: '适用于：复杂生活问题、多方面分析、长期规划、人际关系、重大变化',\n                                        examples: '我该如何处理复杂的人际关系？如何平衡工作和生活？人生下一阶段的规划？',\n                                        tips: '适合需要多角度深入分析的复杂问题，会提供非常详细的指导'\n                                    },\n                                    'star': {\n                                        icon: '⭐',\n                                        title: '七芒星牌阵',\n                                        description: '神秘的七芒星形牌阵，探索精神层面的能量流动。帮助你了解内在力量、精神成长和能量平衡。',\n                                        usage: '适用于：精神成长、内在探索、能量平衡、灵性问题、创意启发',\n                                        examples: '我的精神状态如何？如何提升自己的能量？我的天赋是什么？',\n                                        tips: '适合探索内在世界和精神层面的问题，注重心灵成长和自我认知'\n                                    },\n                                    'pyramid': {\n                                        icon: '🔺',\n                                        title: '金字塔牌阵',\n                                        description: '六张牌组成的金字塔形牌阵，层次递进地揭示问题的深层含义。从基础到顶峰，逐步深入问题核心。',\n                                        usage: '适用于：目标实现、层次分析、逐步规划、技能提升、个人成长',\n                                        examples: '如何实现我的目标？我需要提升哪些能力？如何一步步改善现状？',\n                                        tips: '适合需要分步骤解决的问题，会提供循序渐进的建议和指导'\n                                    },\n                                    'moon': {\n                                        icon: '🌙',\n                                        title: '月相牌阵',\n                                        description: '四张牌对应月亮的四个相位，探索情感周期和直觉指引。特别适合了解情感变化和内在直觉。',\n                                        usage: '适用于：情感周期、直觉开发、内心声音、情绪管理、女性问题',\n                                        examples: '我的情绪为什么会波动？如何倾听内心的声音？我的直觉在告诉我什么？',\n                                        tips: '适合探索情感和直觉相关的问题，特别关注内在感受和情绪变化'\n                                    },\n                                    'chakra': {\n                                        icon: '🧘',\n                                        title: '脉轮牌阵',\n                                        description: '七张牌对应人体七个脉轮能量中心，用于能量疗愈和身心平衡。帮助识别能量阻塞和平衡方法。',\n                                        usage: '适用于：身心健康、能量疗愈、情绪平衡、精神净化、整体wellness',\n                                        examples: '我的能量状态如何？哪个方面需要调整？如何保持身心平衡？',\n                                        tips: '适合关注身心健康和能量平衡的问题，会从整体wellness角度给出建议'\n                                    },\n                                    'decision': {\n                                        icon: '⚖️',\n                                        title: '决策牌阵',\n                                        description: '五张牌专门用于重要决策分析，比较不同选择的利弊和后果。帮助你做出明智的选择。',\n                                        usage: '适用于：重要选择、利弊分析、风险评估、机会把握、人生转折',\n                                        examples: '我应该选择A还是B？这个决定的后果是什么？哪个选择对我更有利？',\n                                        tips: '适合面临重要选择时使用，需要明确说明你在考虑的具体选项'\n                                    }\n                                };\n                                const currentSpread = spreadDetails[selectedType];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-purple-900/60 to-violet-900/60 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl mr-4\",\n                                                        children: currentSpread.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-amber-300\",\n                                                                children: currentSpread.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-200 mt-2\",\n                                                                children: currentSpread.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-amber-200 mb-3\",\n                                                                children: \"\\uD83D\\uDCCB 适用场景\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-100 text-sm leading-relaxed\",\n                                                                children: currentSpread.usage\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1054,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-amber-200 mb-3\",\n                                                                children: \"\\uD83D\\uDCA1 提问建议\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1059,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-100 text-sm leading-relaxed\",\n                                                                children: currentSpread.tips\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1053,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-amber-200 mb-3\",\n                                                        children: \"\\uD83C\\uDF1F 问题示例\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1065,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-100 text-sm leading-relaxed italic\",\n                                                        children: currentSpread.examples\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1064,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1042,\n                                    columnNumber: 17\n                                }, this);\n                            })()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'shuffle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-6\",\n                                children: \"\\uD83D\\uDD2E\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1077,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                children: \"正在洗牌...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1078,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-200 mb-8\",\n                                children: \"请静心等待，让宇宙的能量流动\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1079,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-6 gap-4 mb-8\",\n                                        children: Array.from({\n                                            length: 12\n                                        }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-[63px] h-[96px] bg-gradient-to-br from-purple-800 to-purple-900 rounded-[6px] border border-amber-400 transform transition-all duration-500 \".concat(shuffling ? 'animate-pulse scale-110 rotate-12' : ''),\n                                                style: {\n                                                    animationDelay: \"\".concat(i * 100, \"ms\")\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full overflow-hidden rounded-lg\",\n                                                    children: renderCardback(selectedCardback, false)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1094,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1084,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1082,\n                                        columnNumber: 15\n                                    }, this),\n                                    shuffling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl animate-spin\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1102,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1081,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1076,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'draw' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83C\\uDFB4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"选择你的牌\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-4\",\n                                        children: [\n                                            \"请凭直觉选择 \",\n                                            maxCards,\n                                            \" 张牌\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-300\",\n                                        children: [\n                                            \"已选择: \",\n                                            selectedCards.length,\n                                            \"/\",\n                                            maxCards\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center gap-4 mt-6 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCardLayout('circle'),\n                                                className: \"px-4 py-2 rounded-lg transition-all duration-300 \".concat(cardLayout === 'circle' ? 'bg-amber-400 text-purple-900 font-bold' : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'),\n                                                children: \"\\uD83D\\uDD2E 圆形\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCardLayout('grid'),\n                                                className: \"px-4 py-2 rounded-lg transition-all duration-300 \".concat(cardLayout === 'grid' ? 'bg-amber-400 text-purple-900 font-bold' : 'bg-purple-800/50 text-purple-200 hover:bg-purple-700/50'),\n                                                children: \"\\uD83D\\uDCF1 网格\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1112,\n                                columnNumber: 13\n                            }, this),\n                            renderCardLayout(),\n                            selectedCards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-amber-300 mb-2\",\n                                                children: \"已选择的卡牌\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-200 text-sm\",\n                                                children: \"点击卡牌可以取消选择\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center gap-4 max-w-4xl mx-auto\",\n                                        children: selectedCards.map((cardIndex, index)=>{\n                                            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n                                            const card = allCards[cardIndex];\n                                            const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n                                            const imageUrl = uploadedImages[card.id] || card.imageUrl;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center cursor-pointer transform transition-all duration-300 hover:scale-105\",\n                                                onClick: ()=>{\n                                                    setSelectedCards(selectedCards.filter((i)=>i !== cardIndex));\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-3 border border-amber-400/30 min-w-[100px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-bold text-amber-300 mb-2\",\n                                                            children: [\n                                                                \"第\",\n                                                                index + 1,\n                                                                \"张\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-24 mx-auto mb-2 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                            children: imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: imageUrl,\n                                                                alt: card.name,\n                                                                className: \"w-full h-full object-cover\",\n                                                                onError: (e)=>{\n                                                                    e.currentTarget.style.display = 'none';\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 31\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg\",\n                                                                children: \"\\uD83C\\uDFB4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1189,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1178,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-semibold text-white truncate\",\n                                                            children: card.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1171,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, cardIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1164,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1149,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentStep('question'),\n                                    className: \"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors mr-4\",\n                                    children: \"重新选择问题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1206,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1111,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'interpreting' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-6\",\n                                children: \"\\uD83D\\uDD2E\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                children: \"神秘力量正在解读...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-200 mb-8\",\n                                children: \"请稍候，神秘的智慧正在为你揭示答案\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-6 mb-8 max-w-6xl mx-auto \".concat(revealedCards.length === 1 ? 'justify-center' : revealedCards.length <= 3 ? 'justify-center' : 'justify-center'),\n                                children: revealedCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-bold text-amber-300 mb-3\",\n                                                    children: card.position\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                    children: card.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: card.imageUrl,\n                                                        alt: card.name,\n                                                        className: \"w-full h-full object-cover transition-transform duration-300 scale-110 \".concat(card.orientation === '逆位' ? 'rotate-180' : ''),\n                                                        onError: ()=>{\n                                                        // 图片加载失败时会自动显示备用内容\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1238,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83C\\uDFB4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1236,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-semibold text-white mb-1\",\n                                                    children: card.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1254,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-purple-200\",\n                                                    children: card.orientation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1255,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1229,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-900/40 rounded-full h-2 mb-3 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-amber-400 to-amber-600 h-full rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(interpretingProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-purple-300 mb-2\",\n                                        children: [\n                                            \"解读进度: \",\n                                            Math.round(interpretingProgress),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-purple-200 text-sm\",\n                                        children: [\n                                            interpretingProgress < 30 && \"🌟 正在连接宇宙能量...\",\n                                            interpretingProgress >= 30 && interpretingProgress < 60 && \"✨ 分析牌面含义...\",\n                                            interpretingProgress >= 60 && interpretingProgress < 90 && \"🔮 生成专属解读...\",\n                                            interpretingProgress >= 90 && \"💫 即将完成...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1262,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.4s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1283,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'cardback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83C\\uDFA8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"卡背设计选择\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: \"选择您喜欢的塔罗牌背面设计风格\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1296,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\",\n                                children: [\n                                    {\n                                        id: 'classic',\n                                        name: '经典神秘',\n                                        desc: '传统紫色+金色，神秘符号',\n                                        preview: '⚜'\n                                    },\n                                    {\n                                        id: 'luxury',\n                                        name: '奢华金箔',\n                                        desc: '黑金配色，双重边框',\n                                        preview: '🌟'\n                                    },\n                                    {\n                                        id: 'sacred',\n                                        name: '神圣几何',\n                                        desc: '宗教符号，神圣图案',\n                                        preview: '🕎'\n                                    },\n                                    {\n                                        id: 'cosmic',\n                                        name: '宇宙星空',\n                                        desc: '蓝色星空，天体元素',\n                                        preview: '🌌'\n                                    },\n                                    {\n                                        id: 'elegant',\n                                        name: '优雅简约',\n                                        desc: '灰色系，简洁几何',\n                                        preview: '◇'\n                                    },\n                                    {\n                                        id: 'royal',\n                                        name: '皇室华丽',\n                                        desc: '紫金配色，皇室元素',\n                                        preview: '👑'\n                                    },\n                                    {\n                                        id: 'minimal',\n                                        name: '极简现代',\n                                        desc: '黑白简约，现代美学',\n                                        preview: '◯'\n                                    },\n                                    {\n                                        id: 'ai-generated',\n                                        name: 'AI专业版',\n                                        desc: '您的AI生成专业卡背',\n                                        preview: '🤖'\n                                    }\n                                ].map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2 \".concat(selectedCardback === style.id ? 'ring-2 ring-amber-400 ring-offset-2 ring-offset-purple-900' : ''),\n                                        onClick: ()=>selectCardback(style.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-6 border border-amber-400/20 shadow-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-amber-300 mb-2 text-center\",\n                                                    children: style.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1359,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-200 text-sm text-center mb-6\",\n                                                    children: style.desc\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1360,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center mb-6\",\n                                                    children: renderCardback(style.id, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1363,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: selectedCardback === style.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-amber-400 to-amber-600 px-4 py-2 rounded-full text-white font-bold text-sm\",\n                                                        children: \"✅ 已选择\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-800/50 hover:bg-purple-700/50 px-4 py-2 rounded-full text-purple-200 font-medium text-sm transition-colors\",\n                                                        children: \"点击选择\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1374,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1368,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1358,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, style.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1351,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-amber-300 mb-6 text-center\",\n                                        children: \"当前选择预览\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center gap-8 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-purple-200 mb-4\",\n                                                        children: \"反面（卡背）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1390,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: renderCardback(selectedCardback, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1389,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl text-amber-300\",\n                                                children: \"↔️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1396,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-lg font-semibold text-purple-200 mb-4\",\n                                                        children: \"正面（示例）\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1399,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-32 h-48 mx-auto bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl mb-2\",\n                                                                    children: \"\\uD83C\\uDFB4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1402,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-amber-200\",\n                                                                    children: \"愚人牌\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1403,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1401,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1400,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-purple-300 text-sm\",\n                                            children: \"选择的卡背设计将应用到所有塔罗牌的反面显示\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1410,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1385,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // 保存选择并返回首页\n                                            if (true) {\n                                                localStorage.setItem('selectedCardback', selectedCardback);\n                                            }\n                                            resetReading();\n                                        },\n                                        className: \"bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg mr-4\",\n                                        children: \"✅ 应用选择\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>startPreview(),\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                        children: \"\\uD83C\\uDFB4 预览效果\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1429,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1415,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1292,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83C\\uDFB4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"塔罗牌预览\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: \"查看和测试您的78张塔罗牌图片效果\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1444,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center gap-4 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setAllCardsFlipped(false);\n                                                    setFlippedCards(new Set());\n                                                },\n                                                className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                                children: \"\\uD83D\\uDD04 全部反面\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1448,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setAllCardsFlipped(true);\n                                                    const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n                                                    setFlippedCards(new Set(allCards.map((card)=>card.id.toString())));\n                                                },\n                                                className: \"bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                                children: \"✨ 全部正面\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleAllCards,\n                                                className: \"bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                                children: \"\\uD83D\\uDD00 一键翻转\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1447,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center gap-6\",\n                                        children: (()=>{\n                                            const allCards = (0,_constants_tarot_cards__WEBPACK_IMPORTED_MODULE_3__.getAllCards)();\n                                            const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n                                            return allCards.map((card)=>{\n                                                const isFlipped = allCardsFlipped || flippedCards.has(card.id.toString());\n                                                const imageUrl = uploadedImages[card.id] || card.imageUrl;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px] cursor-pointer transform transition-all duration-500 hover:scale-105 hover:-translate-y-2 group\",\n                                                        onClick: ()=>toggleSingleCard(card.id.toString()),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-bold text-amber-300 mb-3\",\n                                                                children: card.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1496,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center relative\",\n                                                                children: [\n                                                                    isFlipped ? // 正面 - 显示卡牌图片\n                                                                    imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: imageUrl,\n                                                                        alt: card.name,\n                                                                        className: \"w-full h-full object-cover transition-transform duration-300 scale-110\",\n                                                                        onError: (e)=>{\n                                                                            // 图片加载失败时显示备用内容\n                                                                            e.currentTarget.style.display = 'none';\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1505,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl\",\n                                                                        children: \"\\uD83C\\uDFB4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1515,\n                                                                        columnNumber: 33\n                                                                    }, this) : // 反面 - 使用用户选择的卡背样式\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full h-full\",\n                                                                        children: renderCardback(selectedCardback, false)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1519,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-1 -right-1 w-5 h-5 bg-amber-400 rounded-full flex items-center justify-center text-xs font-bold text-purple-900 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                                        children: isFlipped ? '↻' : '↺'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1525,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1501,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-200\",\n                                                                        children: isFlipped ? '正面' : '反面'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1532,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    imageUrl && isFlipped && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-emerald-300 mt-1\",\n                                                                        children: \"\\uD83D\\uDCF7 已上传\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1536,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1531,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1491,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, card.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1490,\n                                                    columnNumber: 23\n                                                }, this);\n                                            });\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1478,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center space-x-6 bg-purple-800/30 rounded-2xl px-6 py-3 border border-amber-400/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-purple-200\",\n                                                    children: [\n                                                        \"总计: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-amber-300 font-bold\",\n                                                            children: \"78张\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1550,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1549,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-purple-200\",\n                                                    children: [\n                                                        \"已翻开: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-amber-300 font-bold\",\n                                                            children: [\n                                                                flippedCards.size,\n                                                                \"张\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1553,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1552,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-purple-200\",\n                                                    children: [\n                                                        \"有图片: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-amber-300 font-bold\",\n                                                            children: [\n                                                                (()=>{\n                                                                    const uploadedImages =  true ? JSON.parse(localStorage.getItem('uploadedImages') || '{}') : 0;\n                                                                    return Object.keys(uploadedImages).length;\n                                                                })(),\n                                                                \"张\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1556,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1555,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1548,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1547,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 text-center text-sm text-purple-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"\\uD83D\\uDCA1 点击任意卡牌可以单独翻转 \\xb7 使用上方按钮批量控制\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"\\uD83D\\uDCF1 已上传的图片会自动显示 \\xb7 未上传的显示默认图标\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1571,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1440,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 'result' && currentReading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-6\",\n                                        children: \"\\uD83D\\uDD2E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1580,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold mb-6 text-amber-300\",\n                                        children: \"占卜结果\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-200 mb-8\",\n                                        children: [\n                                            '你的问题: \"',\n                                            question,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1579,\n                                columnNumber: 13\n                            }, this),\n                            currentReading.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-red-400 mb-8\",\n                                children: currentReading.error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1586,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap justify-center gap-6 mb-8 max-w-6xl mx-auto \".concat(revealedCards.length === 1 ? 'justify-center' : revealedCards.length <= 3 ? 'justify-center' : 'justify-center'),\n                                        children: revealedCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-2xl p-4 border border-amber-400/20 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-bold text-amber-300 mb-3\",\n                                                            children: getPositionName(index)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1599,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-31 mx-auto mb-3 bg-gradient-to-br from-amber-400/20 to-purple-600/20 rounded-lg border-2 border-amber-400/30 overflow-hidden flex items-center justify-center\",\n                                                            children: card.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: card.imageUrl,\n                                                                alt: card.name,\n                                                                className: \"w-full h-full object-cover transition-transform duration-300 scale-110 \".concat(card.orientation === '逆位' ? 'rotate-180' : ''),\n                                                                onError: ()=>{\n                                                                // 图片加载失败时会自动显示备用内容\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1606,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl\",\n                                                                children: \"\\uD83C\\uDFB4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1617,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1604,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-semibold text-white mb-1\",\n                                                            children: card.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1622,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: card.orientation\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1623,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1598,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1597,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1591,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-purple-900/40 to-violet-900/40 backdrop-blur-md rounded-3xl p-8 border border-amber-400/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-amber-300 mb-6 text-center\",\n                                                children: \"解读\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1630,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-purple-100 leading-relaxed whitespace-pre-line\",\n                                                children: formatText(currentReading.interpretation)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1631,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mt-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetReading,\n                                    className: \"bg-gradient-to-r from-amber-400 to-amber-600 hover:from-amber-500 hover:to-amber-700 px-8 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105 shadow-lg\",\n                                    children: \"重新占卜 ✨\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1639,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1638,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1578,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 654,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\塔罗牌\\\\tarot-tools\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 629,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"7QpznT2giKPLWa3LIkfw+w2/mQU=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});