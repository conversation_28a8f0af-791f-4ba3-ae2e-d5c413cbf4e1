# 🤖 AI塔罗解读配置指南

## 功能说明

本塔罗牌系统支持对接真实的AI服务来进行个性化解读，包括：
- ✅ **OpenAI GPT-4** - 最强大的AI解读
- ✅ **DeepSeek Chat** - 性价比高的国产AI
- ✅ **本地备用解读** - 无需API密钥即可使用

## 快速配置

### 1. 创建环境变量文件

在项目根目录创建 `.env.local` 文件：

```bash
# 选择AI服务 (openai 或 deepseek)
NEXT_PUBLIC_AI_PROVIDER=deepseek

# 你的API密钥
NEXT_PUBLIC_AI_API_KEY=你的API密钥

# API基础URL (DeepSeek)
NEXT_PUBLIC_AI_BASE_URL=https://api.deepseek.com

# 模型名称
NEXT_PUBLIC_AI_MODEL=deepseek-chat
```

### 2. 获取API密钥

#### DeepSeek (推荐)
1. 访问 [https://platform.deepseek.com](https://platform.deepseek.com)
2. 注册账号并完成认证
3. 在API密钥页面创建新密钥
4. 复制密钥到环境变量

#### OpenAI
1. 访问 [https://platform.openai.com](https://platform.openai.com)
2. 登录账号
3. 在API Keys页面创建新密钥
4. 复制密钥到环境变量

```bash
# OpenAI配置
NEXT_PUBLIC_AI_PROVIDER=openai
NEXT_PUBLIC_AI_API_KEY=sk-your-openai-key-here
NEXT_PUBLIC_AI_BASE_URL=https://api.openai.com/v1
NEXT_PUBLIC_AI_MODEL=gpt-4
```

### 3. 重启服务器

```bash
npm run dev
```

## 使用方式

1. **选择占卜类型** - 今日运势、爱情、事业、时光之轮
2. **输入你的问题** - 具体描述你想了解的事情
3. **观看洗牌仪式** - 营造神秘氛围
4. **从78张牌中选择** - 凭直觉选择你的牌
5. **获得AI解读** - 基于你的问题和抽牌的个性化解读

## AI解读特色

- 🎯 **个性化** - 根据你的具体问题进行解读
- 🎴 **专业** - 结合塔罗牌含义和正逆位
- 📍 **准确** - 考虑牌在牌阵中的位置意义
- 💡 **实用** - 提供具体建议和人生指引

## 备用方案

如果没有配置AI API密钥，系统会自动使用本地解读：
- ✅ 依然可以正常使用所有功能
- ✅ 提供基础的塔罗牌解读
- ✅ 无需任何配置

## 费用说明

### DeepSeek (推荐)
- 💰 **超低成本** - 约0.0014元/1000tokens
- 🚀 **响应快速** - 通常1-3秒返回结果
- 🇨🇳 **国产服务** - 稳定可靠

### OpenAI GPT-4
- 💰 **较高成本** - 约0.21元/1000tokens
- 🧠 **最强能力** - 解读质量最高
- 🌍 **国际服务** - 需要稳定网络

## 故障排除

### 1. AI解读失败
- 检查API密钥是否正确
- 确认账户余额是否充足
- 检查网络连接是否正常

### 2. 显示"备用解读"
- 表示AI服务不可用，使用了本地解读
- 检查环境变量配置
- 查看浏览器控制台错误信息

### 3. 配置不生效
- 确保文件名为 `.env.local`
- 重启开发服务器
- 检查环境变量名称是否正确

## 安全提醒

- 🔒 **保护API密钥** - 不要提交到git仓库
- 🚫 **限制访问** - 为API密钥设置使用限制
- 📊 **监控使用量** - 定期检查API使用情况

## 技术支持

如果遇到问题，请检查：
1. 环境变量是否正确配置
2. API密钥是否有效
3. 网络连接是否正常
4. 浏览器控制台是否有错误信息
